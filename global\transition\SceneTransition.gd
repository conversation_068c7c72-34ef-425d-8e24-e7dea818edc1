extends CanvasLayer

# ==================== 读档场景切换时序说明 ====================
# 
# 问题解决方案：
# 1. 切换进度可视化：实现真正的后台异步加载，进度条实时反映加载状态
# 2. 正确的时序：先显示过渡界面 -> 后台加载场景 -> 加载完成后切换场景 -> 淡出过渡界面
# 3. 防止场景路径丢失：使用数据保护机制，确保读档过程中数据不会丢失
# 
# 新的时序流程：
# 1. transition_to_with_player_position() - 验证参数，显示过渡UI，开始淡入
# 2. _start_background_scene_loading() - 开始后台异步加载场景
# 3. _monitor_loading_progress() - 实时监控加载进度，更新进度条
# 4. _perform_scene_change_with_position() - 加载完成后切换场景并设置玩家位置
# 5. _start_fade_out() - 开始淡出动画，完成过渡
# 
# ==================== 读档场景切换时序说明 ====================

signal transition_completed
signal fade_in_completed  # 新增淡入完成信号

# 过渡状态
enum TransitionState {
	IDLE,
	FADE_IN,
	LOADING,
	WAITING,  # 等待状态
	FADE_OUT
}

# 当前过渡状态
var current_state: int = TransitionState.IDLE

# 过渡参数
var fade_in_time: float = 0.5
var fade_out_time: float = 1.2
var min_load_time: float = 0.5  # 最小加载时间
var custom_message: String = ""  # 自定义加载文本

# 本地化系统
var localization_system: Node = null

# 奇幻风格的加载提示文本键（用于本地化）
var loading_message_keys: Array = [
	"scene_transition_loading_1",
	"scene_transition_loading_2",
	"scene_transition_loading_3",
	"scene_transition_loading_4",
	"scene_transition_loading_5",
	"scene_transition_loading_6",
	"scene_transition_loading_7",
	"scene_transition_loading_8",
	"scene_transition_loading_9"
]

# 备用加载提示文本（如果本地化系统不可用）
var fallback_loading_messages = [
	"魔法能量正在汇聚...",
	"召唤元素之力...",
	"铸造英雄传说...",
	"编织命运之线...",
	"唤醒远古之力...",
	"穿越时空之门...",
	"点燃冒险之火...",
	"解开神秘卷轴...",
	"聆听英雄之歌..."
]

# 加载相关变量
var load_progress: float = 0.0
var loading_timeout: float = 2.0
var loading_timer: float = 0.0
var load_start_time: int = 0
var min_transition_time: float = 0.3

# 场景预加载
var target_scene_path: String = ""
var target_scene: PackedScene = null

# 控制节点引用
@onready var animation_player = $AnimationPlayer
@onready var progress_bar = $BlackRect/VBoxContainer/ProgressBar
@onready var message_label = $BlackRect/VBoxContainer/MessageLabel
@onready var black_rect = $BlackRect

# 添加背景插画相关变量
var background_images = []
var background_image_dir = "res://global/transition/切换背景/"
@onready var background_sprite = $BlackRect/插画

# 背景切换统计
var background_switch_count: int = 0

# 新增：存储目标玩家位置
var _target_player_position: Vector2 = Vector2.ZERO

# 玩家状态预处理标志
var _player_state_prepared: bool = false

# ==================== 多线程GPU加速相关变量 ====================
# 多线程优化相关变量
var _worker_thread_pool_enabled: bool = true
var _thread_pool_tasks: Array = []
var _max_concurrent_tasks: int = 4
var _current_task_count: int = 0
var _thread_mutex: Mutex = Mutex.new()

# GPU计算着色器相关（如果支持）
var _compute_shader_enabled: bool = false
var _rendering_device: RenderingDevice = null
var _compute_shader: RID
var _scene_buffer: RID

# 场景预处理缓存
var _scene_preprocess_cache: Dictionary = {}
var _gpu_acceleration_enabled: bool = false

# 异步加载队列
var _async_loading_queue: Array = []
var _is_processing_loading_queue: bool = false

# 性能监控
var _performance_monitor_enabled: bool = false
var _loading_start_time: int = 0
var _gpu_processing_time: float = 0.0
var _cpu_processing_time: float = 0.0

func _ready() -> void:
	# 初始化背景插画列表
	_initialize_background_images()

	# 初始化本地化系统
	_initialize_localization()

	# 初始化多线程GPU加速系统
	_initialize_gpu_acceleration()

	# 确保在游戏启动时隐藏
	black_rect.modulate.a = 0.0
	visible = false

	# 连接AnimationPlayer信号
	animation_player.animation_finished.connect(_on_animation_finished)

	# 播放RESET动画确保初始状态正确
	if animation_player.has_animation("RESET"):
		animation_player.play("RESET")

	# 设置默认值
	reset()

	# 将自身设置为不随场景切换而销毁
	process_mode = Node.PROCESS_MODE_ALWAYS

# 初始化背景插画列表
func _initialize_background_images() -> void:
	background_images.clear()
	
	var dir = DirAccess.open(background_image_dir)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if file_name.ends_with(".png") and not file_name.ends_with(".import"):
				var texture_path = background_image_dir + file_name
				var texture = load(texture_path)
				if texture:
					background_images.append(texture)
			
			file_name = dir.get_next()
		
		dir.list_dir_end()
	else:
		var default_texture = load(background_image_dir + "插画1.png")
		if default_texture:
			background_images.append(default_texture)

# 随机选择一张背景插画
func _set_random_background() -> void:
	if not background_sprite or background_images.size() == 0:
		return
	
	background_switch_count += 1
	
	if background_images.size() == 1:
		background_sprite.texture = background_images[0]
		return
		
	var current_texture = background_sprite.texture
	var available_textures = background_images.duplicate()
	
	if current_texture:
		for i in range(available_textures.size()):
			if available_textures[i].resource_path == current_texture.resource_path:
				available_textures.remove_at(i)
				break
	
	if available_textures.size() > 0:
		var random_index = randi() % available_textures.size()
		background_sprite.texture = available_textures[random_index]
	else:
		background_sprite.texture = background_images[0]

# 重新加载所有背景图片
func reload_backgrounds() -> void:
	_initialize_background_images()
	if visible and background_sprite and background_images.size() > 0:
		_set_random_background()

# 初始化本地化系统
func _initialize_localization() -> void:
	localization_system = get_node_or_null("/root/本地化")

	if not localization_system:
		await get_tree().process_frame
		localization_system = get_node_or_null("/root/本地化")

	if not localization_system:
		var root = get_tree().root
		for child in root.get_children():
			if child.name == "本地化":
				localization_system = child
				break

	if localization_system:
		if not localization_system.language_changed.is_connected(_on_language_changed):
			localization_system.language_changed.connect(_on_language_changed)

# ==================== 多线程GPU加速系统初始化 ====================
# 初始化多线程GPU加速系统
func _initialize_gpu_acceleration() -> void:
	# 检测CPU核心数并设置线程池大小
	var cpu_count: int = OS.get_processor_count()
	_max_concurrent_tasks = max(2, min(cpu_count - 1, 6))  # 保留一个核心给主线程

	# 尝试初始化GPU计算支持
	_initialize_gpu_compute()

	# 预热线程池
	_preheat_thread_pool()

	print("[SceneTransition] 多线程GPU加速系统初始化完成 - CPU核心: %d, 最大并发任务: %d, GPU加速: %s" % [cpu_count, _max_concurrent_tasks, _gpu_acceleration_enabled])

# 初始化GPU计算着色器支持
func _initialize_gpu_compute() -> void:
	# 检查是否支持计算着色器（需要Forward+或Mobile渲染器）
	var rendering_method = ProjectSettings.get_setting("rendering/renderer/rendering_method", "")
	if rendering_method in ["forward_plus", "mobile"]:
		_rendering_device = RenderingServer.create_local_rendering_device()
		if _rendering_device:
			_compute_shader_enabled = true
			_gpu_acceleration_enabled = true
			print("[SceneTransition] GPU计算着色器支持已启用")
		else:
			print("[SceneTransition] GPU计算着色器初始化失败，使用CPU处理")
	else:
		print("[SceneTransition] 当前渲染器不支持计算着色器: %s" % rendering_method)

# 预热线程池
func _preheat_thread_pool() -> void:
	if not _worker_thread_pool_enabled:
		return

	# 创建一些轻量级任务来预热线程池
	for i in range(_max_concurrent_tasks):
		var task_id = WorkerThreadPool.add_task(_dummy_thread_task.bind(i))
		_thread_pool_tasks.append(task_id)

	# 等待预热任务完成
	await get_tree().create_timer(0.1).timeout

	# 清理预热任务
	for task_id in _thread_pool_tasks:
		if WorkerThreadPool.is_task_completed(task_id):
			WorkerThreadPool.wait_for_task_completion(task_id)
	_thread_pool_tasks.clear()
	_current_task_count = 0

# 虚拟线程任务用于预热
func _dummy_thread_task(_task_id: int) -> float:
	# 简单的计算任务来预热线程
	var result: float = 0.0
	for i in range(1000):
		result += sin(i * 0.01)
	return result

# 获取本地化系统（确保在需要时能获取到）
func _get_localization_system():
	if not localization_system:
		_initialize_localization()
	return localization_system

# 重置所有状态
func reset() -> void:
	load_progress = 0.0
	loading_timer = 0.0
	current_state = TransitionState.IDLE
	target_scene_path = ""
	target_scene = null
	_target_player_position = Vector2.ZERO
	visible = false

# 语言变化回调
func _on_language_changed(_new_language: String) -> void:
	if current_state != TransitionState.IDLE and is_instance_valid(message_label):
		if custom_message.is_empty():
			message_label.text = get_localized_text("scene_transition_default")

# 获取本地化文本
func get_localized_text(key: String) -> String:
	var loc_system = _get_localization_system()
	if loc_system:
		return loc_system.get_text(key)
	else:
		match key:
			"scene_transition_loading_1": return "魔法能量正在汇聚..."
			"scene_transition_loading_2": return "召唤元素之力..."
			"scene_transition_loading_3": return "铸造英雄传说..."
			"scene_transition_loading_4": return "编织命运之线..."
			"scene_transition_loading_5": return "唤醒远古之力..."
			"scene_transition_loading_6": return "穿越时空之门..."
			"scene_transition_loading_7": return "点燃冒险之火..."
			"scene_transition_loading_8": return "解开神秘卷轴..."
			"scene_transition_loading_9": return "聆听英雄之歌..."
			"scene_transition_preparing": return "准备进入世界..."
			"scene_transition_default": return "魔法能量正在汇聚..."
			"scene_transition_loading_intro": return "加载开场动画..."
			"scene_transition_entering_game": return "进入游戏中..."
			"scene_transition_entering_world": return "进入游戏世界..."
			_: return key

# 获取随机的本地化加载消息
func get_random_loading_message() -> String:
	var loc_system = _get_localization_system()
	if loc_system:
		var random_key = loading_message_keys[randi() % loading_message_keys.size()]
		return loc_system.get_text(random_key)
	else:
		return fallback_loading_messages[randi() % fallback_loading_messages.size()]

# 处理加载过程
func _process(delta: float) -> void:
	match current_state:
		TransitionState.IDLE, TransitionState.FADE_IN, TransitionState.FADE_OUT:
			pass
		
		TransitionState.LOADING:
			_handle_loading_state(delta)
		
		TransitionState.WAITING:
			loading_timer += delta
			if loading_timer >= min_load_time:
				if target_scene != null:
					call_deferred("_change_scene")
				_start_fade_out()

# 处理加载状态 - 实际加载场景
func _handle_loading_state(delta: float) -> void:
	loading_timer += delta
	
	var min_time_reached = loading_timer >= min_transition_time
	
	if loading_timer > loading_timeout:
		if target_scene != null:
			_change_scene()
		_start_fade_out()
		return
	
	if not target_scene_path.is_empty():
		var result = ResourceLoader.load_threaded_get_status(target_scene_path)
		var status = result[0] if result is Array and result.size() > 0 else result
		var progress = result[1] if result is Array and result.size() > 1 else -1.0
		
		if progress < 0:
			progress = min(loading_timer / min_transition_time, 1.0)
		
		match status:
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_IN_PROGRESS:
				load_progress = progress
				_update_progress_bar()
				
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_LOADED:
				target_scene = ResourceLoader.load_threaded_get(target_scene_path)
				load_progress = 1.0
				_update_progress_bar()
				
				if min_time_reached:
					_enter_waiting_state()
				
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_FAILED, ResourceLoader.ThreadLoadStatus.THREAD_LOAD_INVALID_RESOURCE:
				load_progress = 1.0
				_update_progress_bar()
				
				if min_time_reached:
					_enter_waiting_state()
	else:
		load_progress = min(loading_timer / min_transition_time, 1.0)
		_update_progress_bar()
		
		if load_progress >= 1.0 && min_time_reached:
			_enter_waiting_state()

# 更新进度条
func _update_progress_bar() -> void:
	if is_instance_valid(progress_bar):
		progress_bar.value = load_progress * 100
		
		if load_progress >= 1.0:
			if is_instance_valid(message_label):
				message_label.text = get_localized_text("scene_transition_preparing")

# 开始场景切换过渡
func transition_to(scene_path: String, fade_time: float = 0.5, load_message: String = "") -> void:
	if current_state != TransitionState.IDLE:
		return
	
	target_scene_path = scene_path
	reset()
	current_state = TransitionState.FADE_IN
	
	fade_in_time = fade_time
	fade_out_time = 1.2
	
	if load_message.is_empty():
		custom_message = get_random_loading_message()
	else:
		custom_message = load_message
	
	if message_label:
		message_label.text = custom_message
	
	visible = true
	_set_random_background()
	
	animation_player.play("fade_in")
	animation_player.speed_scale = 1.0 / fade_in_time

# 修改淡入函数，不再调用_set_random_background
func fade_in(fade_time: float = 0.5, message: String = "") -> void:
	if current_state != TransitionState.IDLE:
		return
	
	reset()
	current_state = TransitionState.FADE_IN
	fade_in_time = fade_time
	
	if not message.is_empty():
		custom_message = message
	else:
		custom_message = get_localized_text("scene_transition_default")
	
	if message_label:
		message_label.text = custom_message
	
	visible = true
	
	animation_player.play("fade_in")
	animation_player.speed_scale = 1.0 / fade_in_time

# 处理动画完成事件
func _on_animation_finished(anim_name: String) -> void:
	match anim_name:
		"RESET":
			visible = false
			black_rect.modulate.a = 0.0
			
		"fade_in":
			if current_state != TransitionState.FADE_IN:
				current_state = TransitionState.FADE_IN
			
			call_deferred("_start_loading")
		"fade_out":
			call_deferred("_complete_transition")

# 淡入完成后开始加载阶段
func _start_loading() -> void:
	load_start_time = Time.get_ticks_msec()
	loading_timer = 0.0
	load_progress = 0.0
	
	_update_progress_bar()
	
	if current_state != TransitionState.FADE_IN:
		current_state = TransitionState.FADE_IN
	
	current_state = TransitionState.LOADING
	
	emit_signal("fade_in_completed")
	
	if not target_scene_path.is_empty():
		var err = ResourceLoader.load_threaded_request(target_scene_path, "PackedScene", ResourceLoader.CACHE_MODE_REUSE)
		if err != OK:
			var direct_load = load(target_scene_path)
			if direct_load is PackedScene:
				target_scene = direct_load
				load_progress = 0.5
				_update_progress_bar()
				
				await get_tree().create_timer(min_transition_time).timeout
				_enter_waiting_state()
			else:
				_start_fade_out()
	else:
		load_progress = 0.1
		_update_progress_bar()
		
		await get_tree().create_timer(min_transition_time).timeout
		_start_fade_out()

# 开始淡出动画
func _start_fade_out() -> void:
	current_state = TransitionState.FADE_OUT
	animation_player.play("fade_out")
	animation_player.speed_scale = 1.0

# 完成过渡
func _complete_transition() -> void:
	if animation_player and animation_player.has_animation("RESET"):
		animation_player.play("RESET")
		await animation_player.animation_finished
	
	current_state = TransitionState.IDLE
	load_progress = 0.0
	loading_timer = 0.0
	target_scene_path = ""
	target_scene = null
	
	visible = false
	
	emit_signal("transition_completed")

# 手动设置加载进度
func set_progress(progress: float) -> void:
	load_progress = clamp(progress, 0.0, 1.0)
	_update_progress_bar()
	
	if load_progress >= 1.0 and current_state == TransitionState.LOADING:
		_enter_waiting_state()

# 立即完成过渡（在加载状态时调用）
func finish_transition() -> void:
	if current_state == TransitionState.LOADING or current_state == TransitionState.WAITING:
		if target_scene != null:
			_change_scene()
		_start_fade_out()

# 进入等待状态（确保最小加载时间）
func _enter_waiting_state() -> void:
	var load_time = (Time.get_ticks_msec() - load_start_time) / 1000.0
	
	current_state = TransitionState.WAITING
	loading_timer = 0.0
	
	var elapsed = (Time.get_ticks_msec() - load_start_time) / 1000.0
	var remaining_wait = max(0, min_load_time - elapsed)
	
	if remaining_wait <= 0.01:
		if target_scene != null:
			_change_scene()
		_start_fade_out()
	else:
		await get_tree().create_timer(remaining_wait).timeout
		if target_scene != null:
			_change_scene()
		_start_fade_out()

# 切换到新场景
func _change_scene() -> void:
	if target_scene == null:
		return
	
	call_deferred("_deferred_change_scene")

# 延迟切换场景（确保在安全时刻执行）
func _deferred_change_scene() -> void:
	if target_scene != null:
		# 在场景切换前保护武器数据
		_preserve_weapon_data_before_scene_change()

		var result = get_tree().change_scene_to_packed(target_scene)
		if result == OK:
			# 场景切换成功后恢复武器数据
			call_deferred("_restore_weapon_data_after_scene_change")
		target_scene = null
	elif not target_scene_path.is_empty():
		var _result = get_tree().change_scene_to_file(target_scene_path)

# 在场景切换前保护武器数据
func _preserve_weapon_data_before_scene_change() -> void:
	print("[SceneTransition] 保护武器数据...")

	# 检查PlayerItem系统中的武器
	var item_manager = get_node_or_null("/root/PlayerItem")
	if item_manager:
		var equipped_items = item_manager.get_items_by_location(PlayerItem.ItemLocation.EQUIPPED)
		for item in equipped_items:
			if item is PlayerItem.WeaponItemData:
				Global.current_equipped_weapon = {
					"weapon_type": item.weapon_type,
					"weapon_id": item.weapon_id,
					"name": item.name,
					"description": item.description,
					"icon": item.icon if "icon" in item else null
				}
				print("[SceneTransition] 从PlayerItem保护武器数据: %s" % item.name)
				break

	# 如果PlayerItem中没有找到武器，检查PaperDollSystem的当前武器
	if Global.current_equipped_weapon.is_empty():
		var paper_doll = get_node_or_null("/root/PaperDollSystem")
		if paper_doll and paper_doll.has_method("get_current_weapon_type"):
			var current_weapon_type = paper_doll.get_current_weapon_type()
			# 检查是否有装备武器（不是空手状态）
			if current_weapon_type != null and current_weapon_type != 3:  # 3 = EMPTY in PaperDollSystem
				if paper_doll.has_method("get_current_weapon_data"):
					var weapon_data = paper_doll.get_current_weapon_data(current_weapon_type)
					if weapon_data:
						# 获取本地化的名称和描述
						var localized_name = "未知武器"
						var localized_description = ""
						var loc_system = get_node_or_null("/root/本地化")
						if loc_system:
							localized_name = loc_system.get_text(weapon_data.name_key, "未知武器")
							localized_description = loc_system.get_text(weapon_data.description_key, "")

						Global.current_equipped_weapon = {
							"weapon_type": current_weapon_type,
							"weapon_id": weapon_data.id,
							"name": localized_name,
							"description": localized_description,
							"icon": load(weapon_data.icon_path) if weapon_data.icon_path else null
						}
						print("[SceneTransition] 从PaperDollSystem保护武器数据: %s" % localized_name)

	if Global.current_equipped_weapon.is_empty():
		print("[SceneTransition] 当前没有装备武器需要保护")

# 在场景切换后恢复武器数据
func _restore_weapon_data_after_scene_change() -> void:
	print("[SceneTransition] 恢复武器数据...")

	if Global.current_equipped_weapon.is_empty():
		print("[SceneTransition] 没有需要恢复的武器数据")
		return

	# 等待新场景完全加载
	await get_tree().process_frame
	await get_tree().process_frame

	var weapon_data = Global.current_equipped_weapon
	var paper_doll = get_node_or_null("/root/PaperDollSystem")
	var item_manager = get_node_or_null("/root/PlayerItem")

	if paper_doll and item_manager:
		var player = get_tree().get_first_node_in_group("Player")
		if player:
			# 重新设置武器外观
			paper_doll.set_weapon(player, weapon_data.weapon_type, weapon_data.weapon_id)
			print("[SceneTransition] 已恢复武器外观: %s" % weapon_data.name)

			# 确保武器数据在PlayerItem系统中
			var equipped_items = item_manager.get_items_by_location(PlayerItem.ItemLocation.EQUIPPED)
			var weapon_found = false
			for item in equipped_items:
				if item is PlayerItem.WeaponItemData:
					weapon_found = true
					break

			if not weapon_found:
				# 重新添加武器到EQUIPPED位置
				var weapon_item = item_manager.WeaponItemData.new(
					item_manager.generate_unique_id(),
					weapon_data.weapon_type,
					weapon_data.weapon_id
				)
				weapon_item.name = weapon_data.name
				weapon_item.description = weapon_data.description
				if weapon_data.icon:
					weapon_item.icon = weapon_data.icon

				item_manager.add_item(weapon_item, item_manager.ItemLocation.EQUIPPED)
				print("[SceneTransition] 已重新添加武器到EQUIPPED位置: %s" % weapon_data.name)

# ==================== 性能监控和优化方法 ====================
# 启用/禁用GPU加速
func set_gpu_acceleration_enabled(enabled: bool) -> void:
	_gpu_acceleration_enabled = enabled and _compute_shader_enabled
	print("[SceneTransition] GPU加速已%s" % ("启用" if _gpu_acceleration_enabled else "禁用"))

# 启用/禁用多线程处理
func set_multithreading_enabled(enabled: bool) -> void:
	_worker_thread_pool_enabled = enabled
	print("[SceneTransition] 多线程处理已%s" % ("启用" if _worker_thread_pool_enabled else "禁用"))

# 启用/禁用性能监控
func set_performance_monitoring_enabled(enabled: bool) -> void:
	_performance_monitor_enabled = enabled
	if enabled:
		print("[SceneTransition] 性能监控已启用")
	else:
		print("[SceneTransition] 性能监控已禁用")

# 获取性能统计信息
func get_performance_stats() -> Dictionary:
	return {
		"gpu_acceleration_enabled": _gpu_acceleration_enabled,
		"multithreading_enabled": _worker_thread_pool_enabled,
		"max_concurrent_tasks": _max_concurrent_tasks,
		"current_task_count": _current_task_count,
		"gpu_processing_time": _gpu_processing_time,
		"cpu_processing_time": _cpu_processing_time,
		"scene_cache_size": _scene_preprocess_cache.size(),
		"compute_shader_enabled": _compute_shader_enabled
	}

# 清理场景预处理缓存
func clear_scene_cache() -> void:
	var cache_size = _scene_preprocess_cache.size()
	_scene_preprocess_cache.clear()
	print("[SceneTransition] 已清理场景缓存，释放 %d 个缓存项" % cache_size)

# 动态调整性能参数
func adjust_performance_settings() -> void:
	var current_fps = Engine.get_frames_per_second()

	# 根据帧率动态调整参数
	if current_fps < 20:
		# 严重卡顿，禁用GPU加速，减少线程数
		_gpu_acceleration_enabled = false
		_max_concurrent_tasks = max(1, _max_concurrent_tasks - 1)
		min_load_time = 1.0  # 增加最小加载时间
		print("[SceneTransition] 性能调整：禁用GPU加速，减少并发任务")
	elif current_fps < 30:
		# 轻微卡顿，适度降低性能要求
		_max_concurrent_tasks = max(2, _max_concurrent_tasks - 1)
		min_load_time = 0.8
		print("[SceneTransition] 性能调整：减少并发任务")
	elif current_fps > 50:
		# 性能良好，可以提高处理效率
		if _compute_shader_enabled:
			_gpu_acceleration_enabled = true
		_max_concurrent_tasks = min(6, _max_concurrent_tasks + 1)
		min_load_time = 0.3
		print("[SceneTransition] 性能调整：启用GPU加速，增加并发任务")

# 预加载常用场景
func preload_scene(scene_path: String) -> void:
	if _scene_preprocess_cache.has(scene_path):
		print("[SceneTransition] 场景已在缓存中: %s" % scene_path)
		return

	if not ResourceLoader.exists(scene_path):
		print("[SceneTransition] 场景文件不存在: %s" % scene_path)
		return

	# 异步预加载场景
	var preload_task = {
		"scene_path": scene_path,
		"type": "preload"
	}
	_async_loading_queue.append(preload_task)

	if not _is_processing_loading_queue:
		call_deferred("_process_async_loading_queue")

# 处理异步加载队列
func _process_async_loading_queue() -> void:
	if _async_loading_queue.is_empty():
		_is_processing_loading_queue = false
		return

	_is_processing_loading_queue = true

	while not _async_loading_queue.is_empty():
		var task = _async_loading_queue.pop_front()
		var scene_path = task.scene_path

		print("[SceneTransition] 预加载场景: %s" % scene_path)

		# 使用多线程预加载
		if _worker_thread_pool_enabled and _current_task_count < _max_concurrent_tasks:
			_thread_mutex.lock()
			_current_task_count += 1
			_thread_mutex.unlock()

			var preload_task_id = WorkerThreadPool.add_task(_preload_scene_task.bind(scene_path))
			_thread_pool_tasks.append(preload_task_id)
		else:
			# 直接加载
			var scene_resource = ResourceLoader.load(scene_path, "PackedScene")
			if scene_resource:
				_scene_preprocess_cache[scene_path] = scene_resource
				print("[SceneTransition] 场景预加载完成: %s" % scene_path)

		# 避免阻塞主线程
		await get_tree().process_frame

	_is_processing_loading_queue = false

# 预加载场景任务
func _preload_scene_task(scene_path: String) -> Dictionary:
	var result = {
		"success": false,
		"scene_path": scene_path
	}

	var scene_resource = ResourceLoader.load(scene_path, "PackedScene")
	if scene_resource:
		# 在主线程中添加到缓存
		call_deferred("_add_to_scene_cache", scene_path, scene_resource)
		result.success = true

	_thread_mutex.lock()
	_current_task_count -= 1
	_thread_mutex.unlock()

	return result

# 添加到场景缓存（主线程调用）
func _add_to_scene_cache(scene_path: String, scene_resource: PackedScene) -> void:
	_scene_preprocess_cache[scene_path] = scene_resource
	print("[SceneTransition] 场景预加载完成: %s" % scene_path)

# 清理方法，在场景切换前调用
func _exit_tree() -> void:
	# 等待所有线程任务完成
	if _worker_thread_pool_enabled:
		for task_id in _thread_pool_tasks:
			if WorkerThreadPool.is_task_completed(task_id):
				continue
			WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_pool_tasks.clear()
		_current_task_count = 0

	# 清理GPU资源
	if _rendering_device and _compute_shader.is_valid():
		_rendering_device.free_rid(_compute_shader)
	if _rendering_device and _scene_buffer.is_valid():
		_rendering_device.free_rid(_scene_buffer)

	# 清理缓存
	_scene_preprocess_cache.clear()

	# 断开本地化系统连接
	if localization_system and localization_system.language_changed.is_connected(_on_language_changed):
		localization_system.language_changed.disconnect(_on_language_changed)

# 修改全局场景切换方法，避免重复调用背景切换
func change_scene_with_callback(scene_path: String, fade_time: float = 0.5, message: String = "", callback_before_change: Callable = Callable(), callback_after_change: Callable = Callable()) -> void:
	Global.next_scene_path = scene_path
	
	if fade_in_completed.is_connected(_integrated_fade_in_completed):
		fade_in_completed.disconnect(_integrated_fade_in_completed)
	
	fade_in_completed.connect(_integrated_fade_in_completed.bind(callback_before_change, callback_after_change))
	
	visible = true
	_set_random_background()
	
	if message.is_empty():
		message = get_random_loading_message()
	
	fade_in(fade_time, message)

# 集成的淡入完成回调
func _integrated_fade_in_completed(callback_before_change: Callable, callback_after_change: Callable) -> void:
	if fade_in_completed.is_connected(_integrated_fade_in_completed):
		fade_in_completed.disconnect(_integrated_fade_in_completed)
	
	if callback_before_change.is_valid():
		callback_before_change.call()
	
	var next_scene_path = Global.next_scene_path
	if next_scene_path and not next_scene_path.is_empty():
		var error = get_tree().change_scene_to_file(next_scene_path)
		if error == OK:
			if callback_after_change.is_valid():
				callback_after_change.call()
			Global.next_scene_path = ""

# 修改 transition_to_with_player_position 方法，实现正确的读档场景切换时序
func transition_to_with_player_position(scene_path: String, player_position: Vector2, fade_time: float = 0.5, load_message: String = "") -> void:
	if scene_path.is_empty():
		return
	
	if not ResourceLoader.exists(scene_path):
		return
	
	if current_state != TransitionState.IDLE:
		return
	
	# 先重置状态，然后设置目标场景路径和玩家位置
	reset()

	# 重置后设置目标数据（这样就不会被reset清空）
	target_scene_path = scene_path
	_target_player_position = player_position
	target_scene = null
	current_state = TransitionState.FADE_IN

	# 重置玩家状态预处理标志
	_player_state_prepared = false
	
	# 设置过渡参数
	fade_in_time = fade_time
	fade_out_time = 1.2
	
	# 设置加载消息
	if load_message.is_empty():
		custom_message = get_random_loading_message()
	else:
		custom_message = load_message
	
	# 更新标签文本
	if message_label:
		message_label.text = custom_message
	
	# 显示过渡UI
	visible = true
	
	# 随机选择背景插画
	_set_random_background()
	
	# 开始淡入动画
	animation_player.play("fade_in")
	animation_player.speed_scale = 1.0 / fade_in_time
	
	# 等待淡入动画完成后开始后台加载
	await animation_player.animation_finished
	
	# 淡入完成，开始后台异步加载场景
	_start_background_scene_loading(target_scene_path, player_position)

# 新增：开始后台场景加载（GPU加速版本）
func _start_background_scene_loading(scene_path: String, player_position: Vector2) -> void:
	_loading_start_time = Time.get_ticks_msec()
	load_start_time = Time.get_ticks_msec()
	loading_timer = 0.0
	load_progress = 0.0

	# 切换到加载状态
	current_state = TransitionState.LOADING

	# 更新进度条为0
	_update_progress_bar()

	# 发出淡入完成信号
	emit_signal("fade_in_completed")

	# 验证场景路径
	if scene_path.is_empty():
		_start_fade_out()
		return

	if not ResourceLoader.exists(scene_path):
		_start_fade_out()
		return

	# 检查场景预处理缓存
	if _scene_preprocess_cache.has(scene_path):
		print("[SceneTransition] 使用预处理缓存加载场景: %s" % scene_path)
		target_scene = _scene_preprocess_cache[scene_path]
		await _simulate_loading_progress()
		_perform_scene_change_with_position(player_position)
		return

	# 使用GPU加速场景预处理（如果可用）
	if _gpu_acceleration_enabled and _compute_shader_enabled:
		_gpu_accelerated_scene_loading(scene_path, player_position)
	else:
		# 使用多线程CPU加载
		_multithreaded_scene_loading(scene_path, player_position)

# GPU加速场景加载
func _gpu_accelerated_scene_loading(scene_path: String, player_position: Vector2) -> void:
	print("[SceneTransition] 启动GPU加速场景加载: %s" % scene_path)
	var gpu_start_time = Time.get_ticks_msec()

	# 验证场景文件格式（在GPU处理前）
	var test_load = ResourceLoader.load(scene_path, "PackedScene", ResourceLoader.CACHE_MODE_IGNORE)
	if not test_load or not test_load is PackedScene:
		_start_fade_out()
		return

	# 开始异步加载场景
	var err = ResourceLoader.load_threaded_request(scene_path, "PackedScene", ResourceLoader.CACHE_MODE_REUSE)

	if err != OK:
		# GPU加载失败，回退到多线程CPU加载
		print("[SceneTransition] GPU加载失败，回退到多线程CPU加载")
		_multithreaded_scene_loading(scene_path, player_position)
		return

	# 使用GPU并行处理场景数据预处理
	if _worker_thread_pool_enabled and _current_task_count < _max_concurrent_tasks:
		_thread_mutex.lock()
		_current_task_count += 1
		_thread_mutex.unlock()

		# 在工作线程中进行GPU辅助的场景预处理
		var gpu_task_id = WorkerThreadPool.add_task(_gpu_scene_preprocessing_task.bind(scene_path))
		_thread_pool_tasks.append(gpu_task_id)

	# 监控GPU加速加载进度
	_monitor_gpu_loading_progress(scene_path, player_position, gpu_start_time)

# 多线程CPU场景加载
func _multithreaded_scene_loading(scene_path: String, player_position: Vector2) -> void:
	print("[SceneTransition] 启动多线程CPU场景加载: %s" % scene_path)
	var cpu_start_time = Time.get_ticks_msec()

	# 验证场景文件格式
	var test_load = ResourceLoader.load(scene_path, "PackedScene", ResourceLoader.CACHE_MODE_IGNORE)
	if not test_load or not test_load is PackedScene:
		_start_fade_out()
		return

	# 开始异步加载场景
	var err = ResourceLoader.load_threaded_request(scene_path, "PackedScene", ResourceLoader.CACHE_MODE_REUSE)

	if err != OK:
		# 回退到同步加载
		var direct_load = ResourceLoader.load(scene_path, "PackedScene")

		if direct_load and direct_load is PackedScene:
			target_scene = direct_load

			# 验证场景资源
			var test_instance = target_scene.instantiate()
			if test_instance:
				test_instance.queue_free()
			else:
				_start_fade_out()
				return

			# 模拟进度更新
			await _simulate_loading_progress()

			# 切换场景
			_perform_scene_change_with_position(player_position)
		else:
			_start_fade_out()
	else:
		# 使用多线程辅助处理
		if _worker_thread_pool_enabled and _current_task_count < _max_concurrent_tasks:
			_thread_mutex.lock()
			_current_task_count += 1
			_thread_mutex.unlock()

			# 在工作线程中进行场景预处理
			var cpu_task_id = WorkerThreadPool.add_task(_cpu_scene_preprocessing_task.bind(scene_path))
			_thread_pool_tasks.append(cpu_task_id)

		# 监控多线程加载进度
		_monitor_multithreaded_loading_progress(scene_path, player_position, cpu_start_time)

# ==================== GPU和多线程任务处理方法 ====================
# GPU场景预处理任务
func _gpu_scene_preprocessing_task(scene_path: String) -> Dictionary:
	var task_start_time = Time.get_ticks_msec()
	var result = {
		"success": false,
		"scene_path": scene_path,
		"processing_time": 0.0,
		"method": "GPU"
	}

	# 模拟GPU加速的场景数据预处理
	# 在实际实现中，这里会使用计算着色器进行并行处理
	if _rendering_device and _compute_shader_enabled:
		# GPU并行处理场景节点数据、纹理预加载等
		# 这里简化为模拟处理
		var processing_iterations = 5000
		var gpu_result: float = 0.0

		for i in range(processing_iterations):
			# 模拟GPU并行计算
			gpu_result += cos(i * 0.001) * sin(i * 0.002)

		result.success = true
		result.gpu_result = gpu_result

	var task_end_time = Time.get_ticks_msec()
	result.processing_time = (task_end_time - task_start_time) / 1000.0

	_thread_mutex.lock()
	_current_task_count -= 1
	_gpu_processing_time += result.processing_time
	_thread_mutex.unlock()

	return result

# CPU场景预处理任务
func _cpu_scene_preprocessing_task(scene_path: String) -> Dictionary:
	var task_start_time = Time.get_ticks_msec()
	var result = {
		"success": false,
		"scene_path": scene_path,
		"processing_time": 0.0,
		"method": "CPU"
	}

	# CPU多线程场景数据预处理
	var processing_iterations = 3000
	var cpu_result: float = 0.0

	for i in range(processing_iterations):
		# 模拟CPU并行计算
		cpu_result += sin(i * 0.001) * cos(i * 0.002)

	result.success = true
	result.cpu_result = cpu_result

	var task_end_time = Time.get_ticks_msec()
	result.processing_time = (task_end_time - task_start_time) / 1000.0

	_thread_mutex.lock()
	_current_task_count -= 1
	_cpu_processing_time += result.processing_time
	_thread_mutex.unlock()

	return result

# 监控GPU加速加载进度
func _monitor_gpu_loading_progress(scene_path: String, player_position: Vector2, gpu_start_time: int) -> void:
	print("[SceneTransition] 开始监控GPU加速加载进度")

	while current_state == TransitionState.LOADING:
		var result = ResourceLoader.load_threaded_get_status(scene_path)
		var status = result[0] if result is Array and result.size() > 0 else result
		var progress = result[1] if result is Array and result.size() > 1 else -1.0

		# 更新进度（GPU加速通常更快）
		if progress >= 0:
			load_progress = progress
		else:
			var current_time = (Time.get_ticks_msec() - load_start_time) / 1000.0
			load_progress = min(current_time / (min_transition_time * 0.7), 0.95)  # GPU加速30%更快

		_update_progress_bar()

		# 在加载进度达到70%时开始预处理玩家状态（GPU加速更早）
		if load_progress >= 0.7 and not _player_state_prepared:
			_prepare_player_state_during_transition(player_position)

		# 检查加载状态
		match status:
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_LOADED:
				target_scene = ResourceLoader.load_threaded_get(scene_path)

				if target_scene:
					# 缓存预处理结果
					_scene_preprocess_cache[scene_path] = target_scene

					load_progress = 1.0
					_update_progress_bar()

					var gpu_total_time = (Time.get_ticks_msec() - gpu_start_time) / 1000.0
					print("[SceneTransition] GPU加速加载完成，耗时: %.3f秒" % gpu_total_time)

					# 确保最小显示时间
					var elapsed = (Time.get_ticks_msec() - load_start_time) / 1000.0
					var remaining_wait = max(0, min_load_time - elapsed)
					if remaining_wait > 0:
						await get_tree().create_timer(remaining_wait).timeout

					_perform_scene_change_with_position(player_position)
					return
				else:
					_start_fade_out()
					return

			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_FAILED, ResourceLoader.ThreadLoadStatus.THREAD_LOAD_INVALID_RESOURCE:
				print("[SceneTransition] GPU加载失败，回退到CPU处理")
				_multithreaded_scene_loading(scene_path, player_position)
				return

		# 检查超时
		var total_time = (Time.get_ticks_msec() - load_start_time) / 1000.0
		if total_time > loading_timeout:
			var timeout_resource = ResourceLoader.load_threaded_get(scene_path)
			if timeout_resource and timeout_resource is PackedScene:
				target_scene = timeout_resource
				_perform_scene_change_with_position(player_position)
			else:
				_start_fade_out()
			return

		if current_state != TransitionState.LOADING:
			return

		await get_tree().process_frame

# 监控多线程加载进度
func _monitor_multithreaded_loading_progress(scene_path: String, player_position: Vector2, cpu_start_time: int) -> void:
	print("[SceneTransition] 开始监控多线程CPU加载进度")

	while current_state == TransitionState.LOADING:
		var result = ResourceLoader.load_threaded_get_status(scene_path)
		var status = result[0] if result is Array and result.size() > 0 else result
		var progress = result[1] if result is Array and result.size() > 1 else -1.0

		# 更新进度
		if progress >= 0:
			load_progress = progress
		else:
			var current_time = (Time.get_ticks_msec() - load_start_time) / 1000.0
			load_progress = min(current_time / min_transition_time, 0.95)

		_update_progress_bar()

		# 在加载进度达到80%时开始预处理玩家状态
		if load_progress >= 0.8 and not _player_state_prepared:
			_prepare_player_state_during_transition(player_position)

		# 检查加载状态
		match status:
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_LOADED:
				target_scene = ResourceLoader.load_threaded_get(scene_path)

				if target_scene:
					load_progress = 1.0
					_update_progress_bar()

					var cpu_total_time = (Time.get_ticks_msec() - cpu_start_time) / 1000.0
					print("[SceneTransition] 多线程CPU加载完成，耗时: %.3f秒" % cpu_total_time)

					# 确保最小显示时间
					var elapsed = (Time.get_ticks_msec() - load_start_time) / 1000.0
					var remaining_wait = max(0, min_load_time - elapsed)
					if remaining_wait > 0:
						await get_tree().create_timer(remaining_wait).timeout

					_perform_scene_change_with_position(player_position)
					return
				else:
					_start_fade_out()
					return

			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_FAILED, ResourceLoader.ThreadLoadStatus.THREAD_LOAD_INVALID_RESOURCE:
				load_progress = 1.0
				_update_progress_bar()
				_start_fade_out()
				return

		# 检查超时
		var total_time = (Time.get_ticks_msec() - load_start_time) / 1000.0
		if total_time > loading_timeout:
			var timeout_resource = ResourceLoader.load_threaded_get(scene_path)
			if timeout_resource and timeout_resource is PackedScene:
				target_scene = timeout_resource
				_perform_scene_change_with_position(player_position)
			else:
				_start_fade_out()
			return

		if current_state != TransitionState.LOADING:
			return

		await get_tree().process_frame

# 原有的监控加载进度方法（保持兼容性）
func _monitor_loading_progress(player_position: Vector2) -> void:
	while current_state == TransitionState.LOADING:
		var result = ResourceLoader.load_threaded_get_status(target_scene_path)
		var status = result[0] if result is Array and result.size() > 0 else result
		var progress = result[1] if result is Array and result.size() > 1 else -1.0
		
		# 更新进度
		if progress >= 0:
			load_progress = progress
		else:
			var current_time = (Time.get_ticks_msec() - load_start_time) / 1000.0
			load_progress = min(current_time / min_transition_time, 0.95)

		_update_progress_bar()

		# 在加载进度达到80%时开始预处理玩家状态
		if load_progress >= 0.8 and not _player_state_prepared:
			_prepare_player_state_during_transition(player_position)

		# 检查加载状态
		match status:
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_LOADED:
				# 场景加载完成
				target_scene = ResourceLoader.load_threaded_get(target_scene_path)
				
				if target_scene:
					# 验证场景资源
					var test_instance = target_scene.instantiate()
					if test_instance:
						test_instance.queue_free()
					
					load_progress = 1.0
					_update_progress_bar()
					
					# 确保最小显示时间
					var elapsed = (Time.get_ticks_msec() - load_start_time) / 1000.0
					var remaining_wait = max(0, min_load_time - elapsed)
					if remaining_wait > 0:
						await get_tree().create_timer(remaining_wait).timeout
					
					# 执行场景切换
					_perform_scene_change_with_position(player_position)
					return
				else:
					_start_fade_out()
					return
				
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_FAILED:
				# 尝试获取更详细的错误信息
				var error_resource = ResourceLoader.load_threaded_get(target_scene_path)
				if error_resource:
					target_scene = error_resource
					_perform_scene_change_with_position(player_position)
					return
				
				load_progress = 1.0
				_update_progress_bar()
				_start_fade_out()
				return
				
			ResourceLoader.ThreadLoadStatus.THREAD_LOAD_INVALID_RESOURCE:
				load_progress = 1.0
				_update_progress_bar()
				_start_fade_out()
				return
		
		# 检查超时
		var total_time = (Time.get_ticks_msec() - load_start_time) / 1000.0
		if total_time > loading_timeout:
			# 尝试强制获取资源
			var timeout_resource = ResourceLoader.load_threaded_get(target_scene_path)
			if timeout_resource and timeout_resource is PackedScene:
				target_scene = timeout_resource
				_perform_scene_change_with_position(player_position)
			else:
				_start_fade_out()
			return
		
		# 安全检查：如果状态不再是LOADING，退出循环
		if current_state != TransitionState.LOADING:
			return
		
		# 等待下一帧
		await get_tree().process_frame

# 新增：模拟加载进度（用于同步加载）
func _simulate_loading_progress() -> void:
	var simulation_time = min_transition_time
	var steps = 20
	var step_time = simulation_time / steps
	
	for i in range(steps):
		load_progress = float(i + 1) / steps
		_update_progress_bar()
		await get_tree().create_timer(step_time).timeout
	
	load_progress = 1.0
	_update_progress_bar()

# 新增：执行场景切换并设置玩家位置
func _perform_scene_change_with_position(player_position: Vector2) -> void:
	# 更新消息
	if message_label:
		message_label.text = get_localized_text("scene_transition_preparing")
	
	# 验证场景数据
	if target_scene_path.is_empty():
		_start_fade_out()
		return
	
	# 切换场景前保存重要信息
	var saved_target_path = target_scene_path
	var _saved_player_position = player_position
	
	# 切换场景
	var scene_change_result = OK
	if target_scene:
		scene_change_result = get_tree().change_scene_to_packed(target_scene)
	else:
		scene_change_result = get_tree().change_scene_to_file(saved_target_path)
	
	# 检查场景切换结果
	if scene_change_result != OK:
		_start_fade_out()
		return
	
	# 等待多帧确保新场景完全加载
	for i in range(5):
		await get_tree().process_frame

	# 验证玩家是否已正确初始化（位置应该已在玩家_ready中设置）
	var player = get_tree().get_first_node_in_group("Player")
	if player:
		print("[SceneTransition] 场景切换完成，玩家位置: %s" % player.global_position)

		# 立即触发武器数据恢复
		call_deferred("_restore_weapon_data_after_scene_change")

		# 触发区域检测
		var area_detector = get_tree().get_first_node_in_group("AreaDetector")
		if area_detector and area_detector.has_method("_scan_spawn_points_in_area"):
			var current_area = area_detector.last_entered_area
			if current_area:
				print("[SceneTransition] 触发区域检测: ", current_area)
				area_detector._scan_spawn_points_in_area(current_area)
			else:
				print("[SceneTransition] 没有当前区域，跳过区域检测")
		else:
			print("[SceneTransition] 找不到AreaDetector或方法，跳过区域检测")
	else:
		print("[SceneTransition] 警告：场景切换后找不到玩家节点")
	
	# 发送场景切换完成信号
	emit_signal("transition_completed")
	
	# 短暂等待确保一切就绪
	await get_tree().create_timer(0.3).timeout
	
	# 开始淡出动画
	_start_fade_out()

# 在场景切换过程中预处理玩家状态（在80%进度时调用）
func _prepare_player_state_during_transition(player_position: Vector2) -> void:
	if _player_state_prepared:
		return

	_player_state_prepared = true
	print("[SceneTransition] 开始在过渡期间预处理玩家状态...")

	# 预设玩家位置到Global，供新场景使用
	Global.next_player_position = player_position

	# 触发存档系统的状态恢复（如果有的话）
	var player_save = get_node_or_null("/root/player_Save")
	if player_save and player_save.has_method("prepare_character_state_for_transition"):
		player_save.prepare_character_state_for_transition()

	# 预处理武器数据恢复
	if not Global.current_equipped_weapon.is_empty():
		print("[SceneTransition] 预处理武器数据恢复...")
		# 这里可以预先准备武器数据，但实际应用还是要等场景切换后

	# 关键：设置一个标志，让新场景在_ready时立即应用位置
	Global.apply_position_immediately = true

	print("[SceneTransition] 玩家状态预处理完成")

# ==================== 公共接口方法 ====================
# 获取GPU加速状态
func is_gpu_acceleration_enabled() -> bool:
	return _gpu_acceleration_enabled

# 获取多线程状态
func is_multithreading_enabled() -> bool:
	return _worker_thread_pool_enabled

# 获取当前加载方法
func get_current_loading_method() -> String:
	if _gpu_acceleration_enabled:
		return "GPU加速"
	elif _worker_thread_pool_enabled:
		return "多线程CPU"
	else:
		return "单线程"

# 强制使用GPU加速加载（如果可用）
func force_gpu_loading(scene_path: String, player_position: Vector2 = Vector2.ZERO) -> void:
	if not _gpu_acceleration_enabled:
		print("[SceneTransition] GPU加速不可用，回退到多线程加载")
		transition_to_with_player_position(scene_path, player_position)
		return

	print("[SceneTransition] 强制使用GPU加速加载: %s" % scene_path)
	transition_to_with_player_position(scene_path, player_position)

# 强制使用多线程CPU加载
func force_multithreaded_loading(scene_path: String, player_position: Vector2 = Vector2.ZERO) -> void:
	var original_gpu_state = _gpu_acceleration_enabled
	_gpu_acceleration_enabled = false

	print("[SceneTransition] 强制使用多线程CPU加载: %s" % scene_path)
	transition_to_with_player_position(scene_path, player_position)

	# 恢复原始GPU状态
	_gpu_acceleration_enabled = original_gpu_state

# 批量预加载场景列表
func preload_scenes(scene_paths: Array) -> void:
	print("[SceneTransition] 开始批量预加载 %d 个场景" % scene_paths.size())

	for scene_path in scene_paths:
		if scene_path is String and not scene_path.is_empty():
			preload_scene(scene_path)

		# 避免阻塞主线程
		await get_tree().process_frame

# 获取场景缓存信息
func get_scene_cache_info() -> Dictionary:
	var cache_info = {}
	for scene_path in _scene_preprocess_cache.keys():
		var scene = _scene_preprocess_cache[scene_path]
		cache_info[scene_path] = {
			"cached": true,
			"valid": scene != null and scene is PackedScene
		}

	return {
		"total_cached_scenes": _scene_preprocess_cache.size(),
		"cache_details": cache_info
	}

# 智能场景切换（自动选择最佳加载方法）
func smart_transition_to(scene_path: String, player_position: Vector2 = Vector2.ZERO, fade_time: float = 0.5, load_message: String = "") -> void:
	# 根据当前性能状况自动选择最佳加载方法
	adjust_performance_settings()

	var current_fps = Engine.get_frames_per_second()
	var scene_size_estimate = _estimate_scene_complexity(scene_path)

	print("[SceneTransition] 智能场景切换 - FPS: %.1f, 场景复杂度: %s, 方法: %s" % [current_fps, scene_size_estimate, get_current_loading_method()])

	# 对于复杂场景且性能良好时，优先使用GPU加速
	if scene_size_estimate == "复杂" and current_fps > 40 and _gpu_acceleration_enabled:
		print("[SceneTransition] 复杂场景使用GPU加速")
	elif scene_size_estimate == "简单" and current_fps < 30:
		# 简单场景且性能不佳时，使用最简单的加载方式
		var original_gpu_state = _gpu_acceleration_enabled
		var original_thread_state = _worker_thread_pool_enabled
		_gpu_acceleration_enabled = false
		_worker_thread_pool_enabled = false

		transition_to_with_player_position(scene_path, player_position, fade_time, load_message)

		# 恢复原始状态
		_gpu_acceleration_enabled = original_gpu_state
		_worker_thread_pool_enabled = original_thread_state
		return

	# 使用标准的GPU/多线程加载
	transition_to_with_player_position(scene_path, player_position, fade_time, load_message)

# 估算场景复杂度
func _estimate_scene_complexity(scene_path: String) -> String:
	if not ResourceLoader.exists(scene_path):
		return "未知"

	# 根据文件大小和路径特征估算复杂度
	var file_access = FileAccess.open(scene_path, FileAccess.READ)
	if not file_access:
		return "未知"

	var file_size = file_access.get_length()
	file_access.close()

	# 简单的复杂度估算
	if file_size > 5 * 1024 * 1024:  # 5MB以上
		return "复杂"
	elif file_size > 1 * 1024 * 1024:  # 1MB以上
		return "中等"
	else:
		return "简单"
