extends Node

# 调试标志
@export var debug_mode: bool = false  # 是否打印详细调试信息

# 放置物预制体路径
var PLACEMENT_SCENE = null
const PLACEMENT_TIMER_SCRIPT = preload("res://ProjectileFactory/Types/Placement/PlacementTimer.gd")

# 放置物属性
var placement_speed = 0  # 默认速度为0
var placement_lifetime = 10.0
var placement_damage = 6
var placement_scale = Vector2(1, 1)
var placement_radius = 0  # 攻击范围
var placement_tick_interval = 0.5  # 触发间隔时间
var placement_max_health = 10  # 默认生命值

# 跟踪活跃的放置物
var active_placements = []

# 存储从CSV加载的放置物配置
var placement_configs = {}

# 声音管理
var active_sound_count = 0
const MAX_ACTIVE_SOUNDS = 3

# 放置物对象池管理
var electric_ghost_pool := []  # 电磁幻影对象池
var pool_size := 30  # 默认池大小
var reuse_delay := 0.05  # 复用延迟
var pool_warn_threshold := 5  # 警告阈值
var pool_is_prewarmed := false  # 标记池是否已预热
var prewarmed_texture_data := ""  # 存储预热时的玩家纹理数据
var first_use_done := false  # 是否已经第一次使用过（关键标志）

# 对象池容器节点
var pool_container = null

# 标记正在销毁的放置物
var destroying_placements = []

# 实质幻影技能状态标记
var electric_ghost_active = false
var electric_ghost_level = 0

# ==================== 多线程GPU加速相关变量 ====================
# 多线程优化相关变量
var _worker_thread_pool_enabled: bool = true
var _thread_pool_tasks: Array = []
var _max_concurrent_tasks: int = 4
var _current_task_count: int = 0
var _thread_mutex: Mutex = Mutex.new()

# GPU计算着色器相关（如果支持）
var _compute_shader_enabled: bool = false
var _rendering_device: RenderingDevice = null
var _compute_shader: RID
var _gpu_buffer: RID

# 预热优化
var _gpu_acceleration_enabled: bool = false
var _prewarming_in_progress: bool = false
var _gpu_prewarming_time: float = 0.0
var _cpu_prewarming_time: float = 0.0

# 技能状态监控
var _skill_level_check_timer: Timer = null
var _last_checked_skill_level: int = 0

# 脚本级别的全局预热调用
static func preload_everything():
	# 预热需要的场景和资源
	var electric_ghost_scene = load("res://ProjectileFactory/Types/Placement/Electric-ghost.tscn")
	var placement_manager = Engine.get_main_loop().get_root().get_node_or_null("/root/PlacementManagement")
	
	# 如果找到了管理器，尝试预热
	if placement_manager:
		print("--- 全局预热管理器已找到，开始预热流程 ---")
		# 创建一个延迟调用
		placement_manager.call_deferred("manual_reset_pools")
	else:
		print("--- 全局预热管理器未找到，可能还未初始化 ---")
	
	return electric_ghost_scene != null  # 确保函数返回值，这有助于防止编译器优化

# 更新_ready函数以添加全局调用代码
func _ready():
	# 注册单例
	if Engine.is_editor_hint():
		return

	if not OS.has_feature("standalone"):
		await get_tree().process_frame

	# 初始化多线程GPU加速系统
	_initialize_gpu_acceleration()

	# 加载配置文件
	load_placement_configs()

	# 打印可用的放置物ID
	print("可用的放置物ID:", placement_configs.keys())

	# 设置技能状态监控定时器
	_setup_skill_monitoring()

	# 检查实质幻影技能状态，决定是否初始化对象池
	_check_electric_ghost_skill_status()

	# 定期清理对象池中的无效引用，并检查池状态
	var cleanup_timer = Timer.new()
	cleanup_timer.wait_time = 30.0  # 每30秒清理一次
	cleanup_timer.autostart = true
	cleanup_timer.timeout.connect(cleanup_pool_references)
	add_child(cleanup_timer)

# ==================== 多线程GPU加速系统初始化 ====================
# 初始化多线程GPU加速系统
func _initialize_gpu_acceleration() -> void:
	# 检测CPU核心数并设置线程池大小
	var cpu_count: int = OS.get_processor_count()
	_max_concurrent_tasks = max(2, min(cpu_count - 1, 6))  # 保留一个核心给主线程

	# 尝试初始化GPU计算支持
	_initialize_gpu_compute()

	# 预热线程池
	_preheat_thread_pool()

	print("[PlacementManagement] 多线程GPU加速系统初始化完成 - CPU核心: %d, 最大并发任务: %d, GPU加速: %s" % [cpu_count, _max_concurrent_tasks, _gpu_acceleration_enabled])

# 初始化GPU计算着色器支持
func _initialize_gpu_compute() -> void:
	# 检查是否支持计算着色器（需要Forward+或Mobile渲染器）
	var rendering_method = ProjectSettings.get_setting("rendering/renderer/rendering_method", "")
	if rendering_method in ["forward_plus", "mobile"]:
		_rendering_device = RenderingServer.create_local_rendering_device()
		if _rendering_device:
			_compute_shader_enabled = true
			_gpu_acceleration_enabled = true
			print("[PlacementManagement] GPU计算着色器支持已启用")
		else:
			print("[PlacementManagement] GPU计算着色器初始化失败，使用CPU处理")
	else:
		print("[PlacementManagement] 当前渲染器不支持计算着色器: %s" % rendering_method)

# 预热线程池
func _preheat_thread_pool() -> void:
	if not _worker_thread_pool_enabled:
		return

	# 创建一些轻量级任务来预热线程池
	for i in range(_max_concurrent_tasks):
		var task_id = WorkerThreadPool.add_task(_dummy_thread_task.bind(i))
		_thread_pool_tasks.append(task_id)

	# 等待预热任务完成
	await get_tree().create_timer(0.1).timeout

	# 清理预热任务
	for task_id in _thread_pool_tasks:
		if WorkerThreadPool.is_task_completed(task_id):
			WorkerThreadPool.wait_for_task_completion(task_id)
	_thread_pool_tasks.clear()
	_current_task_count = 0

# 虚拟线程任务用于预热
func _dummy_thread_task(_task_id: int) -> float:
	# 简单的计算任务来预热线程
	var result: float = 0.0
	for i in range(1000):
		result += sin(i * 0.01)
	return result

# 设置技能状态监控
func _setup_skill_monitoring() -> void:
	_skill_level_check_timer = Timer.new()
	_skill_level_check_timer.wait_time = 2.0  # 每2秒检查一次技能状态
	_skill_level_check_timer.autostart = true
	_skill_level_check_timer.timeout.connect(_check_electric_ghost_skill_status)
	add_child(_skill_level_check_timer)

# 检查实质幻影技能状态
func _check_electric_ghost_skill_status() -> void:
	var player_skills = get_node_or_null("/root/PlayerSkills")
	if not player_skills:
		return

	var current_level = 0
	if player_skills.has_method("get_skill_level"):
		current_level = player_skills.get_skill_level("Electric_Ghost")

	# 如果技能等级发生变化
	if current_level != _last_checked_skill_level:
		_last_checked_skill_level = current_level
		electric_ghost_level = current_level
		electric_ghost_active = current_level > 0

		print("[PlacementManagement] 实质幻影技能状态更新 - 等级: %d, 激活: %s" % [current_level, electric_ghost_active])

		# 如果技能被激活且对象池未初始化，则初始化对象池
		if electric_ghost_active and electric_ghost_pool.is_empty():
			print("[PlacementManagement] 实质幻影技能已激活，开始初始化对象池...")
			call_deferred("_initialize_electric_ghost_pool")

		# 如果技能被禁用，清理对象池以节省内存
		elif not electric_ghost_active and not electric_ghost_pool.is_empty():
			print("[PlacementManagement] 实质幻影技能已禁用，清理对象池...")
			call_deferred("_cleanup_electric_ghost_pool")

# 延迟初始化对象池（仅在需要时）
func _initialize_electric_ghost_pool() -> void:
	if not electric_ghost_active:
		print("[PlacementManagement] 实质幻影技能未激活，跳过对象池初始化")
		return

	print("[PlacementManagement] 开始初始化电磁幻影对象池...")

	# 初始化对象池
	initialize_object_pools()

	# 确保对象池容器已创建
	if not is_instance_valid(pool_container):
		print("[PlacementManagement] 警告：对象池容器未创建，跳过预热")
		return

	# 创建一个延迟执行的预热操作
	var prewarm_timer = Timer.new()
	prewarm_timer.wait_time = 0.5
	prewarm_timer.one_shot = true
	prewarm_timer.autostart = true
	prewarm_timer.timeout.connect(_gpu_accelerated_prewarming)
	add_child(prewarm_timer)

# 清理电磁幻影对象池
func _cleanup_electric_ghost_pool() -> void:
	print("[PlacementManagement] 清理电磁幻影对象池以节省内存...")
	clear_object_pools()

# 延迟初始化对象池（保持兼容性）
func delayed_initialization() -> void:
	# 这个方法现在由技能状态检查触发，保持兼容性
	if electric_ghost_active:
		_initialize_electric_ghost_pool()
	else:
		print("[PlacementManagement] 实质幻影技能未激活，跳过对象池初始化")

# ==================== GPU加速预热方法 ====================
# GPU加速预热
func _gpu_accelerated_prewarming() -> void:
	if _prewarming_in_progress:
		return

	_prewarming_in_progress = true
	var start_time = Time.get_ticks_msec()

	print("[PlacementManagement] 开始GPU加速预热流程...")

	# 使用GPU加速或多线程CPU预热
	if _gpu_acceleration_enabled and _compute_shader_enabled:
		await _gpu_prewarming_process()
	else:
		await _multithreaded_prewarming_process()

	var end_time = Time.get_ticks_msec()
	var total_time = (end_time - start_time) / 1000.0

	print("[PlacementManagement] 预热完成，总耗时: %.3f秒 (GPU: %.3f秒, CPU: %.3f秒)" % [total_time, _gpu_prewarming_time, _cpu_prewarming_time])

	_prewarming_in_progress = false

# GPU预热处理
func _gpu_prewarming_process() -> void:
	var gpu_start_time = Time.get_ticks_msec()

	print("[PlacementManagement] 使用GPU加速预热...")

	# 获取真实玩家纹理数据
	var texture_info = get_sample_texture_paths()

	# 使用多线程并行处理预热任务
	var prewarming_tasks = []
	var batch_size = max(1, electric_ghost_pool.size() / _max_concurrent_tasks)

	for i in range(0, electric_ghost_pool.size(), batch_size):
		var end_index = min(i + batch_size, electric_ghost_pool.size())
		var batch_ghosts = electric_ghost_pool.slice(i, end_index)

		if _current_task_count < _max_concurrent_tasks:
			_thread_mutex.lock()
			_current_task_count += 1
			_thread_mutex.unlock()

			var task_id = WorkerThreadPool.add_task(_gpu_prewarming_task.bind(batch_ghosts, texture_info))
			prewarming_tasks.append(task_id)

	# 等待所有GPU预热任务完成
	for task_id in prewarming_tasks:
		WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_mutex.lock()
		_current_task_count -= 1
		_thread_mutex.unlock()

	# 执行最终的预热验证
	await _finalize_gpu_prewarming(texture_info)

	var gpu_end_time = Time.get_ticks_msec()
	_gpu_prewarming_time = (gpu_end_time - gpu_start_time) / 1000.0

# GPU预热任务
func _gpu_prewarming_task(ghost_batch: Array, texture_info: Dictionary) -> Dictionary:
	var result = {
		"success": false,
		"processed_count": 0,
		"method": "GPU"
	}

	# 模拟GPU并行处理纹理预加载
	if _rendering_device and _compute_shader_enabled:
		# GPU并行处理纹理数据
		var processing_iterations = 2000
		var gpu_result: float = 0.0

		for i in range(processing_iterations):
			# 模拟GPU并行计算纹理处理
			gpu_result += cos(i * 0.001) * sin(i * 0.002)

		result.gpu_result = gpu_result

	# 在主线程中应用预热结果
	for ghost in ghost_batch:
		if is_instance_valid(ghost):
			call_deferred("_apply_gpu_prewarming_to_ghost", ghost, texture_info)
			result.processed_count += 1

	result.success = true
	return result

# 应用GPU预热结果到幻影
func _apply_gpu_prewarming_to_ghost(ghost: Node2D, texture_info: Dictionary) -> void:
	if not is_instance_valid(ghost):
		return

	# 确保ghost在对象池容器中
	if is_instance_valid(pool_container) and ghost.get_parent() != pool_container:
		if ghost.is_inside_tree():
			ghost.get_parent().remove_child(ghost)
		pool_container.add_child(ghost)

	# 应用纹理预热
	if ghost.has_method("prewarm_with_textures"):
		ghost.prewarm_with_textures(texture_info)
	elif ghost.has_method("mark_as_prewarmed"):
		ghost.mark_as_prewarmed()

# 多线程CPU预热处理
func _multithreaded_prewarming_process() -> void:
	var cpu_start_time = Time.get_ticks_msec()

	print("[PlacementManagement] 使用多线程CPU预热...")

	# 获取真实玩家纹理数据
	var texture_info = get_sample_texture_paths()

	# 使用多线程处理预热
	var prewarming_tasks = []
	var batch_size = max(1, electric_ghost_pool.size() / _max_concurrent_tasks)

	for i in range(0, electric_ghost_pool.size(), batch_size):
		var end_index = min(i + batch_size, electric_ghost_pool.size())
		var batch_ghosts = electric_ghost_pool.slice(i, end_index)

		if _current_task_count < _max_concurrent_tasks:
			_thread_mutex.lock()
			_current_task_count += 1
			_thread_mutex.unlock()

			var task_id = WorkerThreadPool.add_task(_cpu_prewarming_task.bind(batch_ghosts, texture_info))
			prewarming_tasks.append(task_id)

	# 等待所有CPU预热任务完成
	for task_id in prewarming_tasks:
		WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_mutex.lock()
		_current_task_count -= 1
		_thread_mutex.unlock()

	# 执行最终的预热验证
	await _finalize_cpu_prewarming(texture_info)

	var cpu_end_time = Time.get_ticks_msec()
	_cpu_prewarming_time = (cpu_end_time - cpu_start_time) / 1000.0

# CPU预热任务
func _cpu_prewarming_task(ghost_batch: Array, texture_info: Dictionary) -> Dictionary:
	var result = {
		"success": false,
		"processed_count": 0,
		"method": "CPU"
	}

	# CPU多线程处理纹理预加载
	var processing_iterations = 1500
	var cpu_result: float = 0.0

	for i in range(processing_iterations):
		# 模拟CPU并行计算
		cpu_result += sin(i * 0.001) * cos(i * 0.002)

	result.cpu_result = cpu_result

	# 在主线程中应用预热结果
	for ghost in ghost_batch:
		if is_instance_valid(ghost):
			call_deferred("_apply_cpu_prewarming_to_ghost", ghost, texture_info)
			result.processed_count += 1

	result.success = true
	return result

# 应用CPU预热结果到幻影
func _apply_cpu_prewarming_to_ghost(ghost: Node2D, texture_info: Dictionary) -> void:
	if not is_instance_valid(ghost):
		return

	# 确保ghost在对象池容器中
	if is_instance_valid(pool_container) and ghost.get_parent() != pool_container:
		if ghost.is_inside_tree():
			ghost.get_parent().remove_child(ghost)
		pool_container.add_child(ghost)

	# 应用纹理预热
	if ghost.has_method("prewarm_with_textures"):
		ghost.prewarm_with_textures(texture_info)
	elif ghost.has_method("mark_as_prewarmed"):
		ghost.mark_as_prewarmed()

# 极端预热 - 真正模拟一次完整的使用过程（保持兼容性）
func extreme_prewarming() -> void:
	# 如果实质幻影技能未激活，跳过预热
	if not electric_ghost_active:
		print("[PlacementManagement] 实质幻影技能未激活，跳过极端预热")
		return

	# 使用新的GPU加速预热方法
	await _gpu_accelerated_prewarming()

# 最终GPU预热验证
func _finalize_gpu_prewarming(texture_info: Dictionary) -> void:
	print("[PlacementManagement] 执行GPU预热最终验证...")

	# 创建一个假玩家节点进行真实测试
	var dummy_player = Node2D.new()
	dummy_player.name = "GPUPrewarmDummyPlayer"

	# 添加必要的纸娃娃部件
	var parts = ["shoulder1", "armor", "head"]
	for part_name in parts:
		var sprite = Sprite2D.new()
		sprite.name = part_name

		# 尝试加载默认纹理
		var texture_path = "res://data/player%s/1/id_1_1.png" % part_name
		if not ResourceLoader.exists(texture_path):
			texture_path = "res://data/%s/1/id_1_1.png" % part_name

		if ResourceLoader.exists(texture_path):
			sprite.texture = ResourceLoader.load(texture_path)

		dummy_player.add_child(sprite)

	# 临时添加到场景树 - 在远离原点的位置
	dummy_player.global_position = Vector2(10000, 10000)
	add_child(dummy_player)

	# 实际运行一次完整的生成过程 - 在远离原点的位置，不播放声音
	var ghost = spawn_electric_ghost(Vector2(10000, 10000), dummy_player, 1.0, false)

	# 确保不播放音效
	if is_instance_valid(ghost):
		var sound = ghost.get_node_or_null("plecar")
		if sound and sound is AudioStreamPlayer2D:
			sound.stop()

	# 短暂延迟后回收
	await get_tree().create_timer(0.1).timeout

	if is_instance_valid(ghost):
		if ghost.has_method("release"):
			ghost.release()
		else:
			recycle_electric_ghost(ghost)

	# 标记为已真实使用过
	first_use_done = true

	# 确保每个池中对象都被正确标记为已预热
	var prewarmed_count = 0
	for pooled_ghost in electric_ghost_pool:
		if is_instance_valid(pooled_ghost) and pooled_ghost.has_method("mark_as_prewarmed"):
			pooled_ghost.mark_as_prewarmed()
			prewarmed_count += 1

	# 设置预热标志
	if prewarmed_count > 0:
		pool_is_prewarmed = true
		print("[PlacementManagement] GPU预热验证完成，成功预热 %d 个对象" % prewarmed_count)

	# 清理假玩家
	dummy_player.queue_free()

# 最终CPU预热验证
func _finalize_cpu_prewarming(texture_info: Dictionary) -> void:
	print("[PlacementManagement] 执行CPU预热最终验证...")

	# 使用传统方法进行最终验证
	var prewarmed_count = 0
	for pooled_ghost in electric_ghost_pool:
		if is_instance_valid(pooled_ghost):
			if pooled_ghost.has_method("prewarm_with_textures"):
				pooled_ghost.prewarm_with_textures(texture_info)
				prewarmed_count += 1
			elif pooled_ghost.has_method("mark_as_prewarmed"):
				pooled_ghost.mark_as_prewarmed()
				prewarmed_count += 1

	# 设置预热标志
	if prewarmed_count > 0:
		pool_is_prewarmed = true
		first_use_done = true
		print("[PlacementManagement] CPU预热验证完成，成功预热 %d 个对象" % prewarmed_count)

# 强制预热所有池中对象（保持兼容性）
func force_prewarm_all() -> void:
	print("强制预热所有池中对象...")
	
	# 获取真实玩家纹理数据
	var texture_info = get_sample_texture_paths()
	
	var prewarmed_count = 0
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			if ghost.has_method("prewarm_with_textures"):
				ghost.prewarm_with_textures(texture_info)
				prewarmed_count += 1
			elif ghost.has_method("prewarm"):
				ghost.prewarm()
				prewarmed_count += 1
	
	if prewarmed_count > 0:
		pool_is_prewarmed = true
		print("强制预热完成，成功预热 %d 个对象" % prewarmed_count)
	else:
		print("警告：强制预热失败，没有对象成功预热")
	
	# 物理体预热
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			if not ghost.is_inside_tree():
				get_tree().root.add_child(ghost)
				
			# 将ghost放在远离原点的位置进行预热，防止玩家听到音效
			ghost.global_position = Vector2(10000, 10000)
				
			for node_name in ["ProjectileBox", "hurtbox"]:
				var box = ghost.get_node_or_null(node_name)
				if box:
					box.set_deferred("monitoring", true)
					box.set_deferred("monitorable", true)
					var collision = box.get_node_or_null("CollisionShape2D")
					if collision:
						collision.set_deferred("disabled", false)
			var main_collision = ghost.get_node_or_null("CollisionShape2D")
			if main_collision:
				main_collision.set_deferred("disabled", false)
	await get_tree().process_frame
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			for node_name in ["ProjectileBox", "hurtbox"]:
				var box = ghost.get_node_or_null(node_name)
				if box:
					box.set_deferred("monitoring", false)
					box.set_deferred("monitorable", false)
					var collision = box.get_node_or_null("CollisionShape2D")
					if collision:
						collision.set_deferred("disabled", true)
			var main_collision = ghost.get_node_or_null("CollisionShape2D")
			if main_collision:
				main_collision.set_deferred("disabled", true)
			if is_instance_valid(pool_container) and ghost.get_parent() != pool_container:
				if ghost.is_inside_tree():
					ghost.get_parent().remove_child(ghost)
				pool_container.add_child(ghost)

# 初始化对象池（仅在实质幻影技能激活时）
func initialize_object_pools() -> void:
	# 检查实质幻影技能是否激活
	if not electric_ghost_active:
		print("[PlacementManagement] 实质幻影技能未激活，跳过对象池初始化")
		return

	print("[PlacementManagement] 初始化电磁幻影对象池，预实例化对象...")

	# 创建对象池容器节点
	if not is_instance_valid(pool_container):
		pool_container = Node2D.new()
		pool_container.name = "PlacementObjectPool"
		add_child(pool_container)

	# 将对象池容器节点放在远离原点的位置
	pool_container.global_position = Vector2(10000, 10000)

	# 先清理旧池
	clear_object_pools()

	# 根据技能等级调整池大小
	var adjusted_pool_size = _calculate_optimal_pool_size()

	# 预先创建电磁幻影对象池
	var electric_ghost_scene = load("res://ProjectileFactory/Types/Placement/Electric-ghost.tscn")
	if electric_ghost_scene:
		print("[PlacementManagement] 创建 %d 个电磁幻影对象（技能等级: %d）" % [adjusted_pool_size, electric_ghost_level])

		# 使用多线程创建对象池
		if _worker_thread_pool_enabled and adjusted_pool_size > 10:
			await _create_pool_multithreaded(electric_ghost_scene, adjusted_pool_size)
		else:
			_create_pool_single_threaded(electric_ghost_scene, adjusted_pool_size)

	print("[PlacementManagement] 对象池初始化完成，电磁幻影池大小: %d" % electric_ghost_pool.size())
	pool_warn_threshold = int(adjusted_pool_size / 5.0)

# 计算最优对象池大小
func _calculate_optimal_pool_size() -> int:
	var base_size = pool_size

	# 根据技能等级调整池大小
	if electric_ghost_level > 0:
		# 高等级技能可能需要更多对象
		base_size += electric_ghost_level * 5

	# 根据系统性能调整
	var cpu_count = OS.get_processor_count()
	if cpu_count >= 8:
		base_size = int(base_size * 1.5)  # 高性能系统增加50%
	elif cpu_count <= 4:
		base_size = int(base_size * 0.8)  # 低性能系统减少20%

	return max(10, min(base_size, 100))  # 限制在10-100之间

# 多线程创建对象池
func _create_pool_multithreaded(scene: PackedScene, pool_size_to_create: int) -> void:
	var batch_size = max(1, pool_size_to_create / _max_concurrent_tasks)
	var creation_tasks = []

	for i in range(0, pool_size_to_create, batch_size):
		var end_index = min(i + batch_size, pool_size_to_create)
		var batch_count = end_index - i

		if _current_task_count < _max_concurrent_tasks:
			_thread_mutex.lock()
			_current_task_count += 1
			_thread_mutex.unlock()

			var task_id = WorkerThreadPool.add_task(_create_ghost_batch.bind(scene, batch_count, i))
			creation_tasks.append(task_id)

	# 等待所有创建任务完成
	for task_id in creation_tasks:
		var batch_result = WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_mutex.lock()
		_current_task_count -= 1
		_thread_mutex.unlock()

		# 在主线程中添加创建的对象到池
		if batch_result and batch_result is Array:
			for ghost_data in batch_result:
				call_deferred("_add_ghost_to_pool", ghost_data)

# 单线程创建对象池
func _create_pool_single_threaded(scene: PackedScene, pool_size_to_create: int) -> void:
	for i in range(pool_size_to_create):
		var ghost = scene.instantiate()
		ghost.name = "PooledElectricGhost_" + str(i)

		# 添加到容器节点
		if is_instance_valid(pool_container):
			pool_container.add_child(ghost)

		# 确保ghost初始位置在远离原点的位置
		ghost.global_position = Vector2(10000, 10000)

		# 重新连接Timer信号
		reconnect_ghost_timer(ghost)

		# 预先设置属性，禁用所有处理和碰撞
		disable_ghost(ghost)

		# 配置电磁幻影属性
		if "1" in placement_configs:
			var config = placement_configs["1"]
			setup_ghost_with_config(ghost, config)

		# 添加到池
		electric_ghost_pool.append(ghost)

# 创建幻影批次任务
func _create_ghost_batch(scene: PackedScene, batch_count: int, start_index: int) -> Array:
	var batch_data = []

	for i in range(batch_count):
		# 在工作线程中准备数据，实际实例化在主线程进行
		var ghost_data = {
			"scene": scene,
			"name": "PooledElectricGhost_" + str(start_index + i),
			"index": start_index + i
		}
		batch_data.append(ghost_data)

	return batch_data

# 添加幻影到池（主线程调用）
func _add_ghost_to_pool(ghost_data: Dictionary) -> void:
	var scene = ghost_data.scene
	var ghost = scene.instantiate()
	ghost.name = ghost_data.name

	# 添加到容器节点
	if is_instance_valid(pool_container):
		pool_container.add_child(ghost)

	# 确保ghost初始位置在远离原点的位置
	ghost.global_position = Vector2(10000, 10000)

	# 重新连接Timer信号
	reconnect_ghost_timer(ghost)

	# 预先设置属性，禁用所有处理和碰撞
	disable_ghost(ghost)

	# 配置电磁幻影属性
	if "1" in placement_configs:
		var config = placement_configs["1"]
		setup_ghost_with_config(ghost, config)

	# 添加到池
	electric_ghost_pool.append(ghost)

# 清理对象池
func clear_object_pools() -> void:
	print("[PlacementManagement] 清理对象池...")

	# 等待所有线程任务完成
	if _worker_thread_pool_enabled:
		for task_id in _thread_pool_tasks:
			if WorkerThreadPool.is_task_completed(task_id):
				continue
			WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_pool_tasks.clear()
		_current_task_count = 0

	# 清空旧池
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			ghost.queue_free()

	electric_ghost_pool.clear()
	pool_is_prewarmed = false
	first_use_done = false

	print("[PlacementManagement] 对象池已清空")

# ==================== 性能监控和优化方法 ====================
# 获取性能统计信息
func get_performance_stats() -> Dictionary:
	return {
		"electric_ghost_active": electric_ghost_active,
		"electric_ghost_level": electric_ghost_level,
		"pool_size": electric_ghost_pool.size(),
		"pool_is_prewarmed": pool_is_prewarmed,
		"gpu_acceleration_enabled": _gpu_acceleration_enabled,
		"multithreading_enabled": _worker_thread_pool_enabled,
		"max_concurrent_tasks": _max_concurrent_tasks,
		"current_task_count": _current_task_count,
		"gpu_prewarming_time": _gpu_prewarming_time,
		"cpu_prewarming_time": _cpu_prewarming_time,
		"active_placements": active_placements.size(),
		"compute_shader_enabled": _compute_shader_enabled
	}

# 打印性能状态
func print_performance_status() -> void:
	var stats = get_performance_stats()


# 动态调整性能参数
func adjust_performance_settings() -> void:
	var current_fps = Engine.get_frames_per_second()

	# 根据帧率动态调整参数
	if current_fps < 20:
		# 严重卡顿，禁用GPU加速，减少线程数
		_gpu_acceleration_enabled = false
		_max_concurrent_tasks = max(1, _max_concurrent_tasks - 1)
		pool_size = max(10, pool_size - 5)
		print("[PlacementManagement] 性能调整：禁用GPU加速，减少并发任务")
	elif current_fps < 30:
		# 轻微卡顿，适度降低性能要求
		_max_concurrent_tasks = max(2, _max_concurrent_tasks - 1)
		pool_size = max(15, pool_size - 3)
		print("[PlacementManagement] 性能调整：减少并发任务")
	elif current_fps > 50:
		# 性能良好，可以提高处理效率
		if _compute_shader_enabled:
			_gpu_acceleration_enabled = true
		_max_concurrent_tasks = min(8, _max_concurrent_tasks + 1)
		pool_size = min(60, pool_size + 5)
		print("[PlacementManagement] 性能调整：启用GPU加速，增加并发任务")

# 启用/禁用GPU加速
func set_gpu_acceleration_enabled(enabled: bool) -> void:
	_gpu_acceleration_enabled = enabled and _compute_shader_enabled
	print("[PlacementManagement] GPU加速已%s" % ("启用" if _gpu_acceleration_enabled else "禁用"))

# 启用/禁用多线程处理
func set_multithreading_enabled(enabled: bool) -> void:
	_worker_thread_pool_enabled = enabled
	print("[PlacementManagement] 多线程处理已%s" % ("启用" if _worker_thread_pool_enabled else "禁用"))

# 获取当前处理方法
func get_current_processing_method() -> String:
	if not electric_ghost_active:
		return "技能未激活"
	elif _gpu_acceleration_enabled:
		return "GPU加速"
	elif _worker_thread_pool_enabled:
		return "多线程CPU"
	else:
		return "单线程"

# 强制重新检查技能状态
func force_skill_status_check() -> void:
	_last_checked_skill_level = -1  # 强制重新检查
	_check_electric_ghost_skill_status()

# 清理方法，在场景切换前调用
func _exit_tree() -> void:
	# 等待所有线程任务完成
	if _worker_thread_pool_enabled:
		for task_id in _thread_pool_tasks:
			if WorkerThreadPool.is_task_completed(task_id):
				continue
			WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_pool_tasks.clear()
		_current_task_count = 0

	# 清理GPU资源
	if _rendering_device and _compute_shader.is_valid():
		_rendering_device.free_rid(_compute_shader)
	if _rendering_device and _gpu_buffer.is_valid():
		_rendering_device.free_rid(_gpu_buffer)

	# 清理对象池
	clear_object_pools()

# 预热对象池 - 解决首次使用时卡顿问题
func preheat_object_pools() -> void:
	if pool_is_prewarmed:
		return
	print("正在预热对象池...")
	var texture_info = get_sample_texture_paths()
	var count = min(5, electric_ghost_pool.size())
	var start_time = Time.get_ticks_msec()
	var temp_root = Node2D.new()
	temp_root.name = "TempPrewarmRoot"
	get_tree().root.add_child(temp_root)
	
	# 将预热节点放在远离原点的位置
	temp_root.global_position = Vector2(10000, 10000)
	
	for i in range(count):
		if i >= electric_ghost_pool.size():
			break
		var ghost = electric_ghost_pool[i]
		if is_instance_valid(ghost):
			if ghost.get_parent():
				ghost.get_parent().remove_child(ghost)
			temp_root.add_child(ghost)
			
			# 确保ghost位于远离原点的位置
			ghost.global_position = Vector2(10000, 10000)
			
			if ghost.has_method("prewarm_with_textures"):
				ghost.prewarm_with_textures(texture_info)
			elif ghost.has_method("prewarm"):
				ghost.prewarm()
			else:
				ghost.visible = true
				ghost.process_mode = Node.PROCESS_MODE_INHERIT
				await get_tree().process_frame
				ghost.visible = false
				ghost.process_mode = Node.PROCESS_MODE_DISABLED
			# 激活但不播放音效 - 使用新参数
			if ghost.has_method("activate"):
				ghost.activate(false)  # 传递false表示不播放声音
			if ghost.get_parent():
				ghost.get_parent().remove_child(ghost)
			if is_instance_valid(pool_container):
				pool_container.add_child(ghost)
	# 物理体预热：让所有池内对象的碰撞体激活一帧
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			if not ghost.is_inside_tree():
				get_tree().root.add_child(ghost)
				
			# 确保ghost位于远离原点的位置
			ghost.global_position = Vector2(10000, 10000)
				
			for node_name in ["ProjectileBox", "hurtbox"]:
				var box = ghost.get_node_or_null(node_name)
				if box:
					box.set_deferred("monitoring", true)
					box.set_deferred("monitorable", true)
					var collision = box.get_node_or_null("CollisionShape2D")
					if collision:
						collision.set_deferred("disabled", false)
			var main_collision = ghost.get_node_or_null("CollisionShape2D")
			if main_collision:
				main_collision.set_deferred("disabled", false)
	await get_tree().process_frame
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			for node_name in ["ProjectileBox", "hurtbox"]:
				var box = ghost.get_node_or_null(node_name)
				if box:
					box.set_deferred("monitoring", false)
					box.set_deferred("monitorable", false)
					var collision = box.get_node_or_null("CollisionShape2D")
					if collision:
						collision.set_deferred("disabled", true)
			var main_collision = ghost.get_node_or_null("CollisionShape2D")
			if main_collision:
				main_collision.set_deferred("disabled", true)
			if is_instance_valid(pool_container) and ghost.get_parent() != pool_container:
				if ghost.is_inside_tree():
					ghost.get_parent().remove_child(ghost)
				pool_container.add_child(ghost)
	temp_root.queue_free()
	var end_time = Time.get_ticks_msec()
	var duration = end_time - start_time
	pool_is_prewarmed = true
	print("对象池预热完成，耗时: %d 毫秒" % duration)
	OS.delay_msec(100)
	print("对象池预热完成，引擎将在适当时机执行内存回收")

# 获取场景中的有效纹理路径样本
func get_sample_texture_paths() -> Dictionary:
	var result = {}
	var player = get_node_or_null("/root/Player")
	
	# 如果找不到Player节点，尝试查找其他命名可能
	if not player:
		player = get_tree().get_first_node_in_group("Player")
	
	# 如果仍然找不到，尝试在场景树中查找可能的玩家角色
	if not player:
		var possible_players = []
		for node in get_tree().get_nodes_in_group("Character"):
			possible_players.append(node)
		
		if not possible_players.is_empty():
			player = possible_players[0]
	
	# 如果找到玩家节点，尝试获取其子节点纹理
	if player:
		var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
		var found_textures = 0
		
		for node_name in node_names:
			var node = player.get_node_or_null(node_name)
			if node and node is Sprite2D and node.texture:
				result[node_name] = {
					"path": node.texture.resource_path,
					"flip_h": node.flip_h,
					"frame": node.frame,
					"hframes": node.hframes,
					"vframes": node.vframes
				}
				found_textures += 1
		
		if found_textures > 0:
			print("从玩家角色找到 %d 个纹理路径" % found_textures)
			return result
	
	# 如果无法从玩家角色获取，使用固定路径
	print("无法从场景中获取纹理，使用默认路径")
	result = {
		"shoulder1": {
			"path": "res://data/playershoulder/1/id_5_1.png",
			"flip_h": false,
			"frame": 0,
			"hframes": 1,
			"vframes": 1
		},
		"armor": {
			"path": "res://data/playerarmor/1/id_1_1.png",
			"flip_h": false,
			"frame": 0,
			"hframes": 1,
			"vframes": 1
		}
	}
	
	# 验证路径存在性
	var valid_paths = 0
	for key in result.keys():
		var path = result[key]["path"]
		if ResourceLoader.exists(path):
			valid_paths += 1
		else:
			print("警告: 纹理路径不存在: ", path)
	
	print("检查了 %d 个默认路径，其中 %d 个有效" % [result.size(), valid_paths])
	return result

# 禁用电磁幻影
func disable_ghost(ghost: Node2D) -> void:
	if not is_instance_valid(ghost):
		return
		
	# 设置为不可见
	ghost.visible = false
	
	# 禁用处理
	ghost.process_mode = Node.PROCESS_MODE_DISABLED
	
	# 禁用Timer
	var timer = ghost.get_node_or_null("Timer")
	if timer and timer is Timer:
		timer.stop()
	
	# 标记为未使用
	ghost.set_meta("in_use", false)
	
	# 禁用碰撞区域
	var projectile_box = ghost.get_node_or_null("ProjectileBox")
	if projectile_box:
		projectile_box.monitoring = false
		projectile_box.monitorable = false
		
		var collision = projectile_box.get_node_or_null("CollisionShape2D")
		if collision:
			collision.disabled = true
	
	# 禁用HurtBox
	var hurt_box = ghost.get_node_or_null("hurtbox")
	if hurt_box:
		hurt_box.monitoring = false
		hurt_box.monitorable = false
		
		var collision = hurt_box.get_node_or_null("CollisionShape2D")
		if collision:
			collision.disabled = true

# 使用配置设置电磁幻影
func setup_ghost_with_config(ghost: Node2D, config: Dictionary) -> void:
	# 获取HitBox节点
	var projectile_box = ghost.get_node_or_null("ProjectileBox")
	if projectile_box and projectile_box is HitBox:
		# 应用CSV中的伤害值
		projectile_box.projectile_damage = config.get("damage", 6)
		
		# 应用玩家攻击力和元素力
		projectile_box.apply_player_attack = config.get("apply_player_attack", true)
		projectile_box.apply_element_power = config.get("apply_element_power", true)
	
	# 获取HurtBox节点并预设值
	var hurt_box = ghost.get_node_or_null("hurtbox")
	if hurt_box and hurt_box is HurtBox:
		# 设置放置物标识
		hurt_box.is_placement = true
		
		# 设置不可被击退
		if "can_be_knocked_back" in hurt_box:
			hurt_box.can_be_knocked_back = false
		
		# 设置生命值
		if "max_health" in hurt_box:
			hurt_box.max_health = config.get("max_health", 5)
			hurt_box.health = config.get("max_health", 5)
	
	# 预设定时器
	var timer = ghost.get_node_or_null("Timer")
	if timer and timer is Timer:
		timer.wait_time = config.get("lifetime", 0.3)

# 获取对象池预热状态
func is_pool_prewarmed() -> bool:
	return pool_is_prewarmed

# 设置预热纹理数据
func set_prewarmed_texture_data(texture_data: String) -> void:
	prewarmed_texture_data = texture_data
	print("已设置预热纹理数据，长度:", texture_data.length())

# 从对象池获取电磁幻影
func get_electric_ghost_from_pool() -> Node2D:
	if not is_instance_valid(pool_container):
		initialize_object_pools()
	
	# 遍历对象池，寻找未使用的幻影
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost) and not ghost.get_meta("in_use"):
			# 标记为使用中
			ghost.set_meta("in_use", true)
			
			# 取消禁用处理
			ghost.process_mode = Node.PROCESS_MODE_INHERIT
			
			# 确保Timer连接正确
			reconnect_ghost_timer(ghost)
			
			return ghost
	
	# 如果没有可用对象，动态创建新对象
	print("对象池耗尽，创建新的电磁幻影")
	var electric_ghost_scene = load("res://ProjectileFactory/Types/Placement/Electric-ghost.tscn")
	var ghost = electric_ghost_scene.instantiate()
	ghost.name = "DynamicElectricGhost_" + str(randi())
	ghost.set_meta("in_use", true)
	
	# 重新连接Timer信号
	reconnect_ghost_timer(ghost)
	
	# 添加到池和容器
	if is_instance_valid(pool_container):
		pool_container.add_child(ghost)
	electric_ghost_pool.append(ghost)
	
	return ghost

# 回收电磁幻影到对象池
func recycle_electric_ghost(ghost: Node2D) -> void:
	if not is_instance_valid(ghost):
		return
	
	# 确保已添加到对象池中
	if ghost not in electric_ghost_pool:
		electric_ghost_pool.append(ghost)
	
	# 禁用所有功能
	disable_ghost(ghost)
	
	# 重置位置到对象池容器
	if is_instance_valid(pool_container) and ghost.get_parent() != pool_container:
		if ghost.is_inside_tree():
			ghost.get_parent().remove_child(ghost)
		pool_container.add_child(ghost)

# 清理失效的对象池引用
func cleanup_pool_references() -> void:
	var valid_ghosts = []
	var invalid_count = 0
	
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			valid_ghosts.append(ghost)
		else:
			invalid_count += 1
	
	# 更新池
	if invalid_count > 0:
		print("对象池清理: 移除了", invalid_count, "个无效引用")
		electric_ghost_pool = valid_ghosts
	
	# 检查是否需要添加更多对象
	var unused_count = 0
	for ghost in electric_ghost_pool:
		if not ghost.get_meta("in_use"):
			unused_count += 1
	
	print("对象池状态: 总大小=", electric_ghost_pool.size(), 
		", 可用对象=", unused_count,
		", 场景内活跃=", active_placements.size())
				
	# 如果可用对象过少，创建更多
	if unused_count < pool_warn_threshold and electric_ghost_pool.size() < pool_size * 2:
		print("可用对象过少，添加更多电磁幻影到池")
		var additional = int(pool_size / 4.0)  # 每次添加1/4池大小的新对象
		
		var electric_ghost_scene = load("res://ProjectileFactory/Types/Placement/Electric-ghost.tscn")
		for i in range(additional):
			var ghost = electric_ghost_scene.instantiate()
			ghost.name = "PooledElectricGhost_expansion_" + str(electric_ghost_pool.size())
			
			# 添加到容器
			if is_instance_valid(pool_container):
				pool_container.add_child(ghost)
			
			# 重新连接Timer信号
			reconnect_ghost_timer(ghost)
			
			# 禁用
			disable_ghost(ghost)
			
			# 配置电磁幻影属性
			if "1" in placement_configs:
				var config = placement_configs["1"]
				setup_ghost_with_config(ghost, config)
			
			# 添加到池
			electric_ghost_pool.append(ghost)
		
		print("对象池扩展完成，新大小:", electric_ghost_pool.size())

# 强制使用特定数据预热所有对象
func force_prewarm_with_data(texture_data: Dictionary) -> void:
	print("强制预热对象池，使用玩家提供的纹理数据...")
	var start_time = Time.get_ticks_msec()
	if electric_ghost_pool.is_empty():
		initialize_object_pools()
	var count = min(5, electric_ghost_pool.size())
	var prewarmed_count = 0
	var temp_root = Node2D.new()
	temp_root.name = "TempForcePrewarmRoot"
	temp_root.global_position = Vector2(10000, 10000)  # 在远离原点的位置预热
	get_tree().root.add_child(temp_root)
	for i in range(count):
		if i >= electric_ghost_pool.size():
			break
		var ghost = electric_ghost_pool[i]
		if not is_instance_valid(ghost):
			continue
		if ghost.get_parent():
			ghost.get_parent().remove_child(ghost)
		temp_root.add_child(ghost)
		
		# 确保ghost位于远离原点的位置
		ghost.global_position = Vector2(10000, 10000)
		
		if ghost.has_method("prewarm_with_textures"):
			ghost.prewarm_with_textures(texture_data)
			prewarmed_count += 1
		if ghost.get_parent():
			ghost.get_parent().remove_child(ghost)
		if is_instance_valid(pool_container):
			pool_container.add_child(ghost)
	# 物理体预热
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			if not ghost.is_inside_tree():
				get_tree().root.add_child(ghost)
				
			# 确保ghost位于远离原点的位置
			ghost.global_position = Vector2(10000, 10000)
				
			for node_name in ["ProjectileBox", "hurtbox"]:
				var box = ghost.get_node_or_null(node_name)
				if box:
					box.set_deferred("monitoring", true)
					box.set_deferred("monitorable", true)
					var collision = box.get_node_or_null("CollisionShape2D")
					if collision:
						collision.set_deferred("disabled", false)
			var main_collision = ghost.get_node_or_null("CollisionShape2D")
			if main_collision:
				main_collision.set_deferred("disabled", false)
	await get_tree().process_frame
	for ghost in electric_ghost_pool:
		if is_instance_valid(ghost):
			for node_name in ["ProjectileBox", "hurtbox"]:
				var box = ghost.get_node_or_null(node_name)
				if box:
					box.set_deferred("monitoring", false)
					box.set_deferred("monitorable", false)
					var collision = box.get_node_or_null("CollisionShape2D")
					if collision:
						collision.set_deferred("disabled", true)
			var main_collision = ghost.get_node_or_null("CollisionShape2D")
			if main_collision:
				main_collision.set_deferred("disabled", true)
			if is_instance_valid(pool_container) and ghost.get_parent() != pool_container:
				if ghost.is_inside_tree():
					ghost.get_parent().remove_child(ghost)
				pool_container.add_child(ghost)
	var end_time = Time.get_ticks_msec()
	var duration = end_time - start_time
	pool_is_prewarmed = true
	print("强制预热完成，成功预热 %d 个对象，耗时: %d 毫秒" % [prewarmed_count, duration])

# 重写电磁幻影生成函数，确保使用CSV中的配置
func spawn_electric_ghost(position: Vector2, source_entity: Node2D, element_power_bonus: float = 0.0, play_sound: bool = true) -> Node2D:
	# 检查实质幻影技能是否激活
	if not electric_ghost_active:
		print("[PlacementManagement] 实质幻影技能未激活，无法生成电磁幻影")
		return null

	# 确保对象池已初始化
	if electric_ghost_pool.is_empty():
		print("[PlacementManagement] 对象池未初始化，开始初始化...")
		initialize_object_pools()

		# 如果初始化后仍然为空，说明技能未激活
		if electric_ghost_pool.is_empty():
			print("[PlacementManagement] 对象池初始化失败，技能可能未激活")
			return null

	# 确保对象池容器存在
	if not is_instance_valid(pool_container):
		print("[PlacementManagement] 对象池容器不存在，无法生成电磁幻影")
		return null

	# 首次使用时如未预热，使用GPU加速预热
	if not pool_is_prewarmed:
		print("[PlacementManagement] 对象池未预热，开始GPU加速预热...")
		if _gpu_acceleration_enabled and _compute_shader_enabled:
			# 异步GPU预热，不阻塞当前调用
			call_deferred("_gpu_accelerated_prewarming")
		else:
			# 传统预热方法
			var texture_info = get_sample_texture_paths()
			force_prewarm_with_data(texture_info)

	# 打印详细状态以跟踪使用情况
	var usage_msg = "[PlacementManagement] 正在生成电磁幻影 (技能等级: %d)" % electric_ghost_level
	if first_use_done:
		usage_msg += " (非首次使用)"
	else:
		usage_msg += " (首次使用)"
	print(usage_msg)
	
	# 尝试从对象池获取实例
	var ghost = get_electric_ghost_from_pool()
	if not ghost:
		print("错误：无法从对象池获取电磁幻影")
		return null
	
	# 获取CSV配置
	var ghost_config = {}
	if "1" in placement_configs: # 电磁幻影的ID是1
		ghost_config = placement_configs["1"]
		print("已加载电磁幻影CSV配置: apply_player_attack=", ghost_config.get("apply_player_attack", true), 
			", apply_element_power=", ghost_config.get("apply_element_power", true),
			", damage=", ghost_config.get("damage", 6))
	
	# 如果已经在场景树中但不在正确位置，先移出
	if ghost.is_inside_tree() and ghost.get_parent() != get_tree().root:
		ghost.get_parent().remove_child(ghost)
	
	# 如果不在场景树中，添加到根节点
	if not ghost.is_inside_tree():
		get_tree().root.add_child(ghost)
	
	# 设置生命周期计时器 - 在添加到场景树后设置
	var timer = ghost.get_node_or_null("Timer")
	if timer and timer is Timer:
		# 使用CSV中定义的生命周期，或默认值
		timer.wait_time = ghost_config.get("lifetime", 0.3)
		# 确保重新连接Timer信号
		reconnect_ghost_timer(ghost)
		# 手动启动计时器
		timer.start()
	
	# 设置位置
	ghost.global_position = position
	ghost.set_meta("pending_position", position)
	
	# 设置缩放 - 从CSV读取
	var scale_value = ghost_config.get("scale", 1.0)
	ghost.scale = Vector2(scale_value, scale_value)
	
	# 设置伤害倍率
	var projectile_box = ghost.get_node_or_null("ProjectileBox")
	if projectile_box and projectile_box is HitBox:
		# 获取技能等级 - 优先使用保存的状态变量
		var skill_level = electric_ghost_level
		# 如果状态变量为0，尝试从PlayerSkills获取
		if skill_level == 0:
			var player_skills = get_node_or_null("/root/PlayerSkills")
			if player_skills and player_skills.has_method("get_skill_level"):
				skill_level = player_skills.get_skill_level("Electric_Ghost")
		# 确保等级至少为1
		if skill_level == 0:
			skill_level = 1
		# 计算伤害和元素力
		var base_damage = ghost_config.get("damage", 6)
		# 只在这里做增伤倍率：每级增加10%
		var damage_multiplier = 1.0 + (skill_level * 0.1)
		var damage = max(15, int(Global.player_attack_power * (0.5 + skill_level * 0.1)))
		var element_power = Global.player_element_power * (0.3 + skill_level * 0.1)
		element_power += element_power_bonus
		# 设置为玩家投掷物，自动设置碰撞层和掩码
		if projectile_box and projectile_box is HitBox:
			projectile_box.entity_type = HitBox.EntityType.PLAYER
			
			projectile_box.is_projectile = true
			projectile_box.projectile_damage = int(base_damage * damage_multiplier) + damage
			projectile_box.element_power = element_power
			projectile_box.apply_player_attack = true
			projectile_box.apply_element_power = true
			# 设置owned_by分组，与HurtBox使用相同的ID
			var projectile_id = ghost.get_instance_id()
			var group_name = "owned_by_" + str(projectile_id)
			projectile_box.add_to_group(group_name)
			projectile_box.update_collision_groups()
			projectile_box.update_collision_layers()
			print("[PlacementManagement] 电磁幻影参数: damage=", projectile_box.projectile_damage, 
				", element_power=", projectile_box.element_power, 
				", 元素力加成=", element_power_bonus, 
				", skill_level=", skill_level)
	
	# 设置HurtBox
	var hurt_box = ghost.get_node_or_null("hurtbox")
	if hurt_box and hurt_box is HurtBox:
		hurt_box.is_placement = true
		hurt_box.entity_type = HurtBox.EntityType.PLAYER
		
		# 从CSV读取生命值
		var max_health_value = ghost_config.get("max_health", 5)
		
		# 确保生命值足够
		hurt_box.max_health = max_health_value
		hurt_box.health = max_health_value
		
		# 设置不可被击退 - 确保这个变量存在
		hurt_box.can_be_knocked_back = false
		
		# 修复HurtBox的碰撞设置
		# 重要：确保HurtBox的掩码不与自身的HitBox碰撞
		var projectile_id = ghost.get_instance_id()
		var group_name = "owned_by_" + str(projectile_id)
		hurt_box.add_to_group(group_name)
		
		# 显式设置层和掩码，确保不会与自己的HitBox产生碰撞
		hurt_box.collision_layer = 1  # 玩家层
		hurt_box.collision_mask = 2   # 敌人层
	
	# 确保放置物添加到正确的组
	ghost.add_to_group("PlacementObject")
	ghost.add_to_group("ElectricGhost")
	
	# 使用序列化方式让电磁幻影获取源实体的数据
	if ghost.has_method("setup_from_source"):
		ghost.setup_from_source(source_entity)
	
	# 使用新方法激活电磁幻影，应用纹理和启用碰撞
	if ghost.has_method("activate"):
		ghost.activate(play_sound)  # 传递是否播放声音的参数
		
	# 添加额外检查 - 如果激活后仍然透明，强制设置不透明
	if ghost.modulate.a < 0.5:
		print("警告：检测到电磁幻影透明度低 (", ghost.modulate.a, ")，强制设置为不透明")
		ghost.modulate.a = 1.0
	
	# 检查纹理加载状态
	var texture_loaded = false
	var sprite_count = 0
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	for node_name in node_names:
		var node = ghost.get_node_or_null(node_name)
		if node and node is Sprite2D:
			sprite_count += 1
			if node.texture != null:
				texture_loaded = true
				break
	
	if not texture_loaded:
		print("警告：电磁幻影没有加载任何纹理，检测到", sprite_count, "个精灵节点")
		# 如果没有纹理，尝试使用备用方法
		setup_paper_doll_for_ghost(ghost, source_entity)
	
	# 最后再次检查属性传递是否正确
	if projectile_box and projectile_box is HitBox:
		# 再次强制设置应用标志 - 以防任何其他代码修改了它们
		projectile_box.apply_player_attack = true
		projectile_box.apply_element_power = true
		
	
	# 将ghost添加到活跃放置物列表
	if ghost not in active_placements:
		active_placements.append(ghost)
	
	# 标记为已经使用过
	if not first_use_done:
		first_use_done = true
		print("电磁幻影已首次使用，标记first_use_done = true")
	
	return ghost

# 设置电磁幻影的纸娃娃系统 - 该函数已被电磁幻影内部的setup_from_source取代，保留此函数作为备用
func setup_paper_doll_for_ghost(ghost: Node2D, source_entity: Node2D) -> void:
	# 这个函数已经被替换为更高效的方法，为了兼容性保留
	if ghost.has_method("setup_from_source"):
		ghost.setup_from_source(source_entity)
		return
		
	# 以下是原有的低效实现，仅作为备用
	# 确保节点有效
	if not is_instance_valid(source_entity) or not is_instance_valid(ghost):
		print("警告: 无法应用纸娃娃系统，节点无效")
		return
	
	# 要复制的节点名称列表
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	
	# 复制源实体的纹理和属性
	for node_name in node_names:
		var source_node = source_entity.get_node_or_null(node_name)
		var target_node = ghost.get_node_or_null(node_name)
		
		if source_node and target_node and source_node is Sprite2D and target_node is Sprite2D:
			# 复制纹理和属性
			if source_node.texture:
				target_node.texture = source_node.texture
			target_node.hframes = source_node.hframes
			target_node.vframes = source_node.vframes
			target_node.frame = source_node.frame
			target_node.flip_h = source_node.flip_h
			target_node.visible = true

	# 设置幻影朝向与源实体一致
	if source_entity.has_method("get_blend_position"):
		var blend_pos = source_entity.get_blend_position()
		if blend_pos < 0:
			# 朝左
			for child in ghost.get_children():
				if child is Sprite2D:
					child.flip_h = true
		else:
			# 朝右
			for child in ghost.get_children():
				if child is Sprite2D:
					child.flip_h = false

# 从CSV加载放置物配置
func load_placement_configs():
	var file = FileAccess.open("res://data/placer/placer.csv", FileAccess.READ)
	if not file:
		push_error("无法打开放置物配置文件")
		return
	
	# 跳过标题行
	var header = file.get_csv_line()
	print("CSV表头:", header)  # 打印表头以便调试
	
	# 读取每一行数据
	while not file.eof_reached():
		var data = file.get_csv_line()
		if data.size() <= 1 or data[0].strip_edges() == "":
			continue  # 跳过空行
		
		var id = data[0]
		var config = {}
		
		# 解析CSV字段 - 更新为新的CSV结构
		var index = 0
		for field in header:
			if index < data.size():
				var value = data[index].strip_edges()
				
				# 根据字段名称设置类型
				match field:
					"id", "name_key", "description_key", "scene", "groups":
						config[field] = value
					"speed", "scale", "angle_spread", "lifetime", "destruction_duration", "charge_time", "health_cost", "mana_cost", "stamina_cost", "damage", "tick_interval", "radius", "max_health":
						config[field] = float(value) if value else 0.0
					"projectile_count":
						config[field] = int(value) if value else 1
					"destroy_on_contact", "destroy_animation", "apply_player_attack", "apply_element_power", "is_buff", "is_movable", "show_ui":
						config[field] = value.to_upper() == "TRUE"
			
			index += 1
		
		# 添加默认值
		if not config.has("speed"):
			config["speed"] = 0.0
		if not config.has("projectile_count"):
			config["projectile_count"] = 1
		if not config.has("angle_spread"):
			config["angle_spread"] = 0.0
		if not config.has("lifetime") and config.has("duration"):
			config["lifetime"] = config["duration"]
		if not config.has("destroy_on_contact"):
			config["destroy_on_contact"] = false
		
		# 尝试加载场景资源
		var scene_path = config["scene"]
		if ResourceLoader.exists(scene_path):
			config["scene_resource"] = load(scene_path)
		else:
			print("警告: 无法加载放置物场景:", scene_path, "，使用默认场景")
			if PLACEMENT_SCENE:
				config["scene_resource"] = PLACEMENT_SCENE
		
		placement_configs[id] = config
		print("已加载放置物配置 #", id)
	
	file.close()
	print("共加载了 ", placement_configs.size(), " 个放置物配置")

# 生成放置物
func spawn_placement(position: Vector2, direction: Vector2 = Vector2.RIGHT, from_player: bool = true, 
				   has_physics: bool = false, custom_params: Dictionary = {}) -> Node2D:
	# 如果没有提供位置，尝试使用玩家中心点
	if position == Vector2.ZERO and Global.has_method("get_player_point_position"):
		position = Global.get_player_point_position()
		print("使用玩家中心点作为放置物发射位置:", position)
	
	# 应用自定义参数
	var speed = custom_params.get("speed", placement_speed)
	var damage = custom_params.get("damage", placement_damage)
	var lifetime = custom_params.get("lifetime", placement_lifetime)
	var scale_value = custom_params.get("scale", placement_scale)
	var scene = custom_params.get("scene", PLACEMENT_SCENE)
	var tick_interval = custom_params.get("tick_interval", placement_tick_interval)
	var radius = custom_params.get("radius", placement_radius)
	var is_buff = custom_params.get("is_buff", false)
	var is_movable = custom_params.get("is_movable", false)
	var max_health = custom_params.get("max_health", placement_max_health)
	var show_ui = custom_params.get("show_ui", true)  # 从自定义参数获取show_ui
	
	print("生成放置物: 速度=", speed, " 伤害=", damage, " 生命周期=", lifetime, " 缩放=", scale_value, " 生命值=", max_health)
	
	# 确保场景已加载
	if scene == null:
		push_error("放置物场景未加载")
		return null
	
	# 创建新的放置物实例
	var placement = scene.instantiate()
	
	# 将放置物添加到识别组
	placement.add_to_group("PlacementObject")
	
	# 如果有额外属性，设置它们
	if "tick_interval" in placement:
		placement.tick_interval = tick_interval
	if "radius" in placement:
		placement.radius = radius
	if "is_buff" in placement:
		placement.is_buff = is_buff
	if "is_movable" in placement:
		placement.is_movable = is_movable

	# 在添加到场景树之前设置HitBox属性
	var projectile_box = null

	# 检查所有可能的HitBox节点名称变体
	for node_name in ["ProjectileBox", "projectilebox", "projectile_box", "HitBox", "hitbox", "hit_box"]:
		var node = placement.get_node_or_null(node_name)
		if node and (node is HitBox or (node.get_script() and node.get_script().resource_path.ends_with("hit_box.gd"))):
			projectile_box = node
			break

	# 如果没有找到主节点的HitBox，查找子节点
	if not projectile_box:
		for child in placement.get_children():
			if child is HitBox or (child.get_script() and child.get_script().resource_path.ends_with("hit_box.gd")):
				projectile_box = child
				break

	if projectile_box and projectile_box is HitBox:
		# 设置实体类型
		if from_player:
			projectile_box.entity_type = HitBox.EntityType.PLAYER
		else:
			projectile_box.entity_type = HitBox.EntityType.ENEMY
		
		# 设置伤害值
		projectile_box.projectile_damage = damage
		print("设置放置物伤害: ", damage, " 节点名称: ", projectile_box.name)
		
		# 标记为投射物
		projectile_box.is_projectile = true
		
		# 【关键修复】为放置物设置攻击组ID - 使用延迟调用
		call_deferred("_set_projectile_attack_group_id", projectile_box, custom_params)
		
		# 设置是否应用玩家攻击力和元素力
		if custom_params.has("apply_player_attack"):
			projectile_box.apply_player_attack = custom_params.get("apply_player_attack")
			print("设置放置物apply_player_attack:", custom_params.get("apply_player_attack"))
		
		if custom_params.has("apply_element_power"):
			projectile_box.apply_element_power = custom_params.get("apply_element_power")
			print("设置放置物apply_element_power:", custom_params.get("apply_element_power"))
		
		# 添加组别
		if custom_params.has("groups"):
			var groups = custom_params.get("groups").split(",")
			for group in groups:
				var group_name = group.strip_edges()
				if group_name != "":
					projectile_box.add_to_group(group_name)
					print("将放置物HitBox添加到组:", group_name)
		
		# 更新碰撞分组和层，但不修改碰撞形状的启用状态
		projectile_box.update_collision_groups()
		projectile_box.update_collision_layers()
	
	# 设置hurtbox属性（如果存在）
	var hurt_box = null

	# 检查所有可能的HurtBox节点名称变体
	for node_name in ["hurtbox", "HurtBox", "hurt_box", "Hurtbox"]:
		var node = placement.get_node_or_null(node_name)
		if node and (node is HurtBox or (node.get_script() and node.get_script().resource_path.ends_with("hurt_box.gd"))):
			hurt_box = node
			break

	# 如果没有找到主节点的HurtBox，查找子节点
	if not hurt_box:
		for child in placement.get_children():
			if child is HurtBox or (child.get_script() and child.get_script().resource_path.ends_with("hurt_box.gd")):
				hurt_box = child
				break

	if hurt_box and hurt_box is HurtBox:
		# 设置放置物标识
		hurt_box.is_placement = true
		
		# 正确设置实体类型（保留与生成者的关系）
		if from_player:
			hurt_box.entity_type = HurtBox.EntityType.PLAYER
		else:
			hurt_box.entity_type = HurtBox.EntityType.ENEMY
		
		print("设置放置物标识，HurtBox实体类型:", HurtBox.EntityType.keys()[hurt_box.entity_type])
		
		# 设置不可被击退
		if "can_be_knockback" in hurt_box:
			hurt_box.can_be_knocked_back = false
		
		# 设置生命值
		if "max_health" in hurt_box:
			# 确保使用配置中的生命值，覆盖场景中的默认值
			hurt_box.max_health = max_health 
			hurt_box.health = max_health
			
			# 额外检查确认生命值设置成功
			print("设置放置物生命值:", max_health, " 节点名称:", hurt_box.name, " 确认当前生命值:", hurt_box.health, "/", hurt_box.max_health)
			
		# 连接受伤信号
		if not hurt_box.is_connected("health_changed", Callable(self, "_on_placement_health_changed")):
			hurt_box.connect("health_changed", Callable(self, "_on_placement_health_changed").bind(placement))
			
		# 连接死亡信号
		if not hurt_box.is_connected("health_depleted", Callable(self, "_on_placement_health_depleted")):
			hurt_box.connect("health_depleted", Callable(self, "_on_placement_health_depleted").bind(placement))
	
	# 添加到场景树
	get_tree().root.add_child(placement)
	
	# 设置放置物位置
	placement.global_position = position
	
	# 设置放置物方向和速度
	direction = direction.normalized()
	if placement.has_method("set_velocity") or "velocity" in placement:
		placement.velocity = direction * speed
	
	# 设置放置物是否来自玩家
	if placement.has_method("set_from_player"):
		placement.set_from_player(from_player)
	
	# 设置放置物是否有物理效果
	if placement.has_method("set_has_physics"):
		placement.set_has_physics(has_physics)
	
	# 设置放置物缩放
	placement.scale = scale_value if scale_value is Vector2 else Vector2(scale_value, scale_value)
	
	# 检查并设置Timer
	var existing_timer = placement.get_node_or_null("Timer")
	
	if existing_timer and existing_timer is Timer:
		# 更新现有Timer的等待时间，确保与配置匹配
		existing_timer.wait_time = lifetime
		existing_timer.start()
		
		# 确保Timer连接到销毁函数
		if not existing_timer.is_connected("timeout", Callable(self, "_on_placement_timeout")):
			existing_timer.connect("timeout", Callable(self, "_on_placement_timeout").bind(placement))
		
		print("使用和更新现有Timer节点, 设置生命周期:", lifetime)
	else:
		# 如果没有Timer，创建一个新的
		var timer = Timer.new()
		timer.name = "Timer"
		timer.wait_time = lifetime
		timer.one_shot = true
		placement.add_child(timer)
		timer.connect("timeout", Callable(self, "_on_placement_timeout").bind(placement))
		timer.start()
		print("创建新的Timer节点, 设置生命周期:", lifetime)
	
	# 更新时间条
	var time_bar = placement.get_node_or_null("timeBar")
	if time_bar and time_bar.has_method("set_lifetime"):
		time_bar.set_lifetime(lifetime)
		# 根据show_ui参数设置可见性
		if show_ui:
			time_bar.set_deferred("visible", true)  # 当show_ui为true时，显示timeBar
		else:
			time_bar.set_deferred("visible", false)  # 当show_ui为false时，隐藏timeBar
		print("更新timeBar, 设置生命周期:", lifetime, "可见性:", show_ui)

	# 添加到活跃放置物列表
	active_placements.append(placement)
	
	# 确保HitBox更新了分组
	if projectile_box and projectile_box is HitBox:
		projectile_box.update_collision_groups()
		projectile_box.update_collision_layers()
	
	# 添加生命条和计时器显示
	add_health_and_timer_ui(placement, max_health, lifetime)
	
	# 添加调试：输出Timer和timeBar状态
	if placement:
		var timer = placement.get_node_or_null("Timer")
		var time_bar_debug = placement.get_node_or_null("timeBar")
		if timer:
			print("[调试] 生成放置物 Timer wait_time:", timer.wait_time, " is_stopped:", timer.is_stopped(), " time_left:", timer.time_left)
		if time_bar_debug:
			print("[调试] 生成放置物 timeBar max_value:", time_bar_debug.max_value, " visible:", time_bar_debug.visible)
	
	return placement

# 添加生命条和计时器显示
func add_health_and_timer_ui(_placement: Node2D, _max_health: float, _lifetime: float) -> void:
	# 始终不显示UI，无论配置如何
	return

# 放置物生命值变化处理
func _on_placement_health_changed(_new_health: float, _old_health: float, placement: Node2D) -> void:
	if not is_instance_valid(placement):
		return
		
	# 更新生命条
	var health_bar = placement.get_node_or_null("PlacementUI/HealthBarBG/HealthBar")
	if health_bar:
		var hurt_box = placement.get_node_or_null("hurtbox")
		if hurt_box and hurt_box is HurtBox:
			health_bar.size.x = 80 * (hurt_box.health / hurt_box.max_health)

# 放置物生命值耗尽处理
func _on_placement_health_depleted(placement: Node2D) -> void:
	if not is_instance_valid(placement):
		return
		
	print("放置物生命值耗尽，开始销毁")
	
	# 使用统一销毁管理方法
	handle_placement_destruction(placement)

# 放置物生命周期结束
func _on_placement_timeout(placement):
	if is_instance_valid(placement):
		print("放置物生命周期结束:", placement.name)
		# 使用统一销毁管理方法
		handle_placement_destruction(placement)
	else:
		print("警告: 尝试销毁无效的放置物实例")

# 更新放置物移动 (如果有)
func _process(delta: float) -> void:
	var to_remove = []
	
	for placement in active_placements:
		if not is_instance_valid(placement):
			to_remove.append(placement)
			continue
			
		# 移动放置物 (如果有速度)
		if "velocity" in placement and placement.velocity != Vector2.ZERO and ("is_movable" not in placement or placement.is_movable):
			placement.position += placement.velocity * delta
	
	# 移除无效的放置物
	for placement in to_remove:
		if active_placements.has(placement):
			active_placements.erase(placement)

# 统一的放置物销毁管理
func handle_placement_destruction(placement: Node2D) -> void:
	if not is_instance_valid(placement):
		return
	
	# 如果在销毁列表中，则不重复处理
	if destroying_placements.has(placement):
		print("放置物已经在销毁过程中，跳过重复处理")
		return
	
	# 添加到销毁列表
	destroying_placements.append(placement)
	
	# 检查是否为电磁幻影
	if placement.is_in_group("ElectricGhost") or placement.name.begins_with("PooledElectricGhost") or placement.name.begins_with("DynamicElectricGhost"):
		handle_electric_ghost_destruction(placement)
		return
	
	# 禁用所有碰撞
	disable_placement_collisions(placement)
	
	# 检查是否有动画播放器
	var animation_player = placement.get_node_or_null("AnimationPlayer")
	if animation_player and animation_player.has_animation("Destruction"):
		# 播放销毁动画
		print("播放放置物销毁动画")
		animation_player.play("Destruction")
		
		# 等待动画完成
		var timer = Timer.new()
		timer.one_shot = true
		timer.wait_time = animation_player.get_animation("Destruction").length
		placement.add_child(timer)
		timer.start()
		
		# 使用一次性连接
		timer.timeout.connect(func():
			if is_instance_valid(placement):
				# 从活跃列表移除
				active_placements.erase(placement)
				# 从销毁列表移除
				destroying_placements.erase(placement)
				# 直接移除
				placement.queue_free()
			timer.queue_free()
		)
	else:
		# 没有销毁动画，直接移除
		print("直接移除放置物，没有销毁动画")
		
		# 从活跃列表移除
		active_placements.erase(placement)
		# 从销毁列表移除
		destroying_placements.erase(placement)
		
		# 直接销毁
		placement.queue_free()

# 电磁幻影特殊销毁处理
func handle_electric_ghost_destruction(ghost: Node2D) -> void:
	if not is_instance_valid(ghost):
		return
	
	# 立即禁用所有碰撞区域
	disable_placement_collisions(ghost)
	
	# 如果在销毁列表中，则不重复处理
	if destroying_placements.has(ghost):
		return
	
	# 添加到销毁列表
	destroying_placements.append(ghost)
	
	# 直接使用动画播放器播放销毁动画，完成后回收到对象池
	var animation_player = ghost.get_node_or_null("AnimationPlayer")
	if animation_player and animation_player.has_animation("Destruction"):
		# 取消Timer自动销毁节点的连接
		var timer = ghost.get_node_or_null("Timer")
		if timer and timer is Timer and timer.is_connected("timeout", Callable(ghost, "queue_free")):
			timer.disconnect("timeout", Callable(ghost, "queue_free"))
			timer.stop()
		
		# 如果release方法被调用后会回收对象，则使用动画完成事件
		if ghost.has_method("release"):
			# 确保release方法会回收对象而不是销毁它
			animation_player.play("Destruction")
		else:
			# 如果没有release方法或该方法会销毁对象，则手动添加完成回调
			animation_player.animation_finished.connect(func(anim_name):
				if anim_name == "Destruction":
					# 从活跃列表和销毁列表移除
					destroying_placements.erase(ghost)
					active_placements.erase(ghost)
					# 回收到对象池
					recycle_electric_ghost(ghost)
					# 断开连接以避免内存泄漏
					animation_player.animation_finished.disconnect(Callable(animation_player.animation_finished.get_connections()[0]["callable"].get_object(), animation_player.animation_finished.get_connections()[0]["callable"].get_method()))
			, CONNECT_ONE_SHOT)
			
			animation_player.play("Destruction")
	else:
		# 没有动画播放器或销毁动画，直接回收
		if ghost.has_method("release"):
			ghost.release()
		else:
			# 从活跃列表和销毁列表移除
			destroying_placements.erase(ghost)
			active_placements.erase(ghost)
			# 回收到对象池
			recycle_electric_ghost(ghost)

# 允许从活跃列表移除节点（供电磁幻影脚本调用）
func remove_from_active_placements(node: Node2D) -> void:
	if node in active_placements:
		active_placements.erase(node)
	
	if node in destroying_placements:
		destroying_placements.erase(node)

# 当销毁动画完成时调用 - 不再使用
func _on_destruction_animation_finished(_anim_name: String, _ghost: Node2D) -> void:
	# 此方法已不再使用，由ghost脚本自行处理
	pass

# 禁用放置物的所有碰撞区域 - 优化碰撞体操作
func disable_placement_collisions(placement: Node2D) -> void:
	if not is_instance_valid(placement):
		return
	
	# 禁用HurtBox
	var hurtbox = null
	for node_name in ["hurtbox", "HurtBox", "hurt_box", "Hurtbox"]:
		var node = placement.get_node_or_null(node_name)
		if node and (node is HurtBox or (node.get_script() and node.get_script().resource_path.ends_with("hurt_box.gd"))):
			hurtbox = node
			break
			
	if hurtbox:
		# 禁用碰撞形状
		for child in hurtbox.get_children():
			if child is CollisionShape2D or child is CollisionPolygon2D:
				# 使用set_deferred安全地禁用碰撞
				child.set_deferred("disabled", true)
			
		# 停止处理和接收伤害
		hurtbox.set_process(false)
		hurtbox.set_deferred("monitoring", false)
		hurtbox.set_deferred("monitorable", false)
			
	# 禁用HitBox/ProjectileBox
	var hitbox = null
	for node_name in ["ProjectileBox", "projectilebox", "projectile_box", "HitBox", "hitbox", "hit_box"]:
		var node = placement.get_node_or_null(node_name)
		if node and (node is HitBox or (node.get_script() and node.get_script().resource_path.ends_with("hit_box.gd"))):
			hitbox = node
			break
			
	if hitbox:
		# 禁用碰撞形状
		for child in hitbox.get_children():
			if child is CollisionShape2D or child is CollisionPolygon2D:
				# 使用set_deferred安全地禁用碰撞
				child.set_deferred("disabled", true)
			
		# 停止处理碰撞
		hitbox.set_process(false)
		hitbox.set_deferred("monitoring", false)
		hitbox.set_deferred("monitorable", false)
	
	# 禁用主碰撞体
	var main_collision = placement.get_node_or_null("CollisionShape2D")
	if main_collision and main_collision is CollisionShape2D:
		main_collision.set_deferred("disabled", true)

# 最终销毁放置物
func finalize_placement_destruction(placement: Node2D) -> void:
	if not is_instance_valid(placement):
		return
		
	print("最终销毁放置物:", placement.name)
	
	# 从活跃放置物列表中移除
	active_placements.erase(placement)
	
	# 从销毁列表中移除
	destroying_placements.erase(placement)
	
	# 完全销毁节点
	placement.queue_free()

# 清理所有放置物
func clear_all_placements():
	var placements_to_clear = active_placements.duplicate()
	for placement in placements_to_clear:
		if is_instance_valid(placement):
			handle_placement_destruction(placement)
	
	active_placements.clear()
	print("已清理所有放置物")

# 根据特定模式生成多个放置物
func spawn_placements_pattern(pattern: String, position: Vector2, radius: float = 100.0, count: int = 5, 
							 from_player: bool = true, custom_params: Dictionary = {}) -> Array:
	var placements = []
	var base_angle = 0.0
	
	# 获取初始方向
	if Global.has_method("get_player_facing_direction"):
		var facing = Global.get_player_facing_direction()
		base_angle = facing.angle()
	
	match pattern:
		"circle":
			# 环形排列
			var angle_step = 2.0 * PI / count
			for i in range(count):
				var angle = base_angle + i * angle_step
				var offset = Vector2(cos(angle), sin(angle)) * radius
				var pos = position + offset
				var placement = spawn_placement(pos, Vector2.RIGHT, from_player, false, custom_params)
				if placement:
					placements.append(placement)
		
		"line":
			# 直线排列
			var direction = Vector2(cos(base_angle), sin(base_angle))
			var perpendicular = direction.rotated(PI/2)
			var line_length = radius
			var step = line_length / (count - 1) if count > 1 else 0.0
			
			for i in range(count):
				var pos = position + direction * radius + perpendicular * (i * step - line_length/2)
				var placement = spawn_placement(pos, Vector2.RIGHT, from_player, false, custom_params)
				if placement:
					placements.append(placement)
		
		"spiral":
			# 螺旋形排列
			var angle_step = 2.0 * PI / count * 2
			for i in range(count):
				var dist_ratio = float(i) / count
				var angle = base_angle + i * angle_step
				var offset = Vector2(cos(angle), sin(angle)) * (radius * dist_ratio)
				var pos = position + offset
				var placement = spawn_placement(pos, Vector2.RIGHT, from_player, false, custom_params)
				if placement:
					placements.append(placement)
		
		"grid":
			# 网格排列
			var grid_size = ceil(sqrt(count))
			var cell_size = radius / (grid_size - 1) if grid_size > 1 else float(radius)
			var start_offset = Vector2(cell_size * (grid_size - 1) / 2, cell_size * (grid_size - 1) / 2)
			
			var index = 0
			for y in range(grid_size):
				for x in range(grid_size):
					if index >= count:
						break
					var pos = position + Vector2(x * cell_size, y * cell_size) - start_offset
					var placement = spawn_placement(pos, Vector2.RIGHT, from_player, false, custom_params)
					if placement:
						placements.append(placement)
					index += 1
		
		_:
			# 默认为单个放置物
			var placement = spawn_placement(position, Vector2.RIGHT, from_player, false, custom_params)
			if placement:
				placements.append(placement)
	
	return placements

# 根据ID生成放置物
func spawn_placement_by_id(id: String, position: Vector2, direction: Vector2 = Vector2.RIGHT, from_player: bool = true, 
						  has_physics: bool = false, custom_params: Dictionary = {}) -> Node2D:
	print("[调试] spawn_placement_by_id id:", id, " position:", position, " direction:", direction)
	var result = null
	if not placement_configs.has(id):
		push_error("无法找到放置物ID: " + id)
		return null
	
	# 特殊处理电磁幻影 (ID为"1")
	if id == "1":
		print("[调试] 检测到电磁幻影ID，使用对象池生成")
		# 查找玩家节点作为源实体
		var source_entity = get_node_or_null("/root/Player")
		if not source_entity:
			source_entity = get_tree().get_first_node_in_group("Player")
		
		if not source_entity:
			for node in get_tree().get_nodes_in_group("Character"):
				source_entity = node
				break
		
		# 从自定义参数获取元素力加成
		var element_power_bonus = custom_params.get("element_power_bonus", 0.0)
		
		# 使用电磁幻影专用的生成函数
		return spawn_electric_ghost(position, source_entity, element_power_bonus)
	
	var config = placement_configs[id]
	
	# 合并自定义参数和配置
	var merged_params = custom_params.duplicate()
	
	# 如果配置中有场景资源，使用它
	if config.has("scene_resource") and config["scene_resource"] != null:
		merged_params["scene"] = config["scene_resource"]
	
	# 设置参数
	for key in config.keys():
		if key != "scene" and key != "scene_resource" and key != "id" and not merged_params.has(key):
			merged_params[key] = config[key]

	# 应用武器强化加成到基础属性
	var skills_system = get_node_or_null("/root/PlayerSkills")
	if skills_system and skills_system.has_method("get_weapon_enhancement_bonus"):
		# 应用伤害强化（固定值增加）
		var damage_bonus = skills_system.get_weapon_enhancement_bonus("damage")
		if damage_bonus > 0 and merged_params.has("damage"):
			var original_damage = merged_params["damage"]
			merged_params["damage"] = original_damage + damage_bonus
			print("[放置物管理] spawn_placement_by_id 应用伤害强化: %s -> %s (+%s点)" % [original_damage, merged_params["damage"], damage_bonus])

		# 应用大小强化
		var scale_bonus = skills_system.get_weapon_enhancement_bonus("scale")
		if scale_bonus > 0 and merged_params.has("scale"):
			var original_scale = merged_params["scale"]
			merged_params["scale"] = original_scale + original_scale * scale_bonus
			print("[放置物管理] spawn_placement_by_id 应用大小强化: %s -> %s (+%s%%)" % [original_scale, merged_params["scale"], scale_bonus * 100])

		# 应用生命周期强化
		var lifetime_bonus = skills_system.get_weapon_enhancement_bonus("lifetime")
		if lifetime_bonus > 0 and merged_params.has("lifetime"):
			var original_lifetime = merged_params["lifetime"]
			merged_params["lifetime"] = original_lifetime + original_lifetime * lifetime_bonus
			print("[放置物管理] spawn_placement_by_id 应用生命周期强化: %s -> %s (+%s%%)" % [original_lifetime, merged_params["lifetime"], lifetime_bonus * 100])

		# 应用速度强化（固定值增加）
		var speed_bonus = skills_system.get_weapon_enhancement_bonus("speed")
		if speed_bonus > 0 and merged_params.has("speed"):
			var original_speed = merged_params["speed"]
			if original_speed > 0:  # 只对有速度的放置物应用速度强化
				merged_params["speed"] = original_speed + speed_bonus
				print("[放置物管理] spawn_placement_by_id 应用速度强化: %s -> %s (+%s点)" % [original_speed, merged_params["speed"], speed_bonus])
			else:
				print("[放置物管理] spawn_placement_by_id 速度强化可用，但当前放置物速度为0，跳过")

		# 应用数量强化到projectile_count
		var count_bonus = skills_system.get_weapon_enhancement_bonus("count")
		if count_bonus > 0:
			var original_count = config.get("projectile_count", 1)
			merged_params["projectile_count"] = original_count + count_bonus
			print("[放置物管理] spawn_placement_by_id 应用数量强化: %s -> %s (+%d)" % [original_count, merged_params["projectile_count"], count_bonus])

	# 特别确保max_health正确设置
	if config.has("max_health"):
		merged_params["max_health"] = config["max_health"]
		print("从CSV读取放置物生命值:", config["max_health"], "放置物ID:", id)
	
	# 特别处理apply_player_attack和apply_element_power参数
	if config.has("apply_player_attack"):
		merged_params["apply_player_attack"] = config["apply_player_attack"]
		print("设置放置物apply_player_attack:", config["apply_player_attack"])
	
	if config.has("apply_element_power"):
		merged_params["apply_element_power"] = config["apply_element_power"]
		print("设置放置物apply_element_power:", config["apply_element_power"])
	
	# 特别处理show_ui参数
	if config.has("show_ui"):
		merged_params["show_ui"] = config["show_ui"]
		print("设置放置物show_ui:", config["show_ui"])

	# 检查是否需要生成多个放置物（使用强化后的数量）
	var count = merged_params.get("projectile_count", config.get("projectile_count", 1))
	var base_angle_spread = config.get("angle_spread", 0.0)
	var weapon_angle_spread_bonus = float(custom_params.get("weapon_angle_spread_bonus", 0))
	var angle_spread = base_angle_spread + weapon_angle_spread_bonus
	print("【放置物角度散布计算】基础角度散布:", base_angle_spread, "武器加成:", weapon_angle_spread_bonus, "最终角度散布:", angle_spread)
	print("【放置物数量】使用强化后的数量:", count)
	
	if count > 1 and angle_spread > 0:
		# 根据角度扩散生成多个放置物
		var placements = []
		var total_angle = angle_spread * (PI / 180.0)  # 转换为弧度
		var angle_step = total_angle / (count - 1) if count > 1 else 0.0
		var start_angle = -total_angle / 2
		
		for i in range(count):
			var angle = start_angle + i * angle_step
			var rotated_direction = direction.rotated(angle)
			# 根据方向计算偏移位置
			var offset_position = position + rotated_direction * 20.0  # 小偏移防止重叠
			var placement = spawn_placement(offset_position, rotated_direction, from_player, has_physics, merged_params)
			if placement:
				# 检查show_ui参数，如果为false则隐藏timeBar
				if config.has("show_ui") and not config["show_ui"]:
					var time_bar = placement.get_node_or_null("timeBar")
					if time_bar:
						time_bar.visible = false
				placements.append(placement)
		
		# 返回第一个放置物
		return placements[0] if not placements.is_empty() else null
	else:
		# 生成单个放置物
		result = spawn_placement(position, direction, from_player, has_physics, merged_params)
		
		# 检查show_ui参数，如果为false则隐藏timeBar
		if result and config.has("show_ui") and not config["show_ui"]:
			var time_bar = result.get_node_or_null("timeBar")
			if time_bar:
				time_bar.visible = false

	# 添加调试：输出最终placement的Timer和timeBar状态
	if result:
		var timer = result.get_node_or_null("Timer")
		var time_bar = result.get_node_or_null("timeBar")
		if timer:
			print("[调试] placement_by_id Timer wait_time:", timer.wait_time, " is_stopped:", timer.is_stopped(), " time_left:", timer.time_left)
		if time_bar:
			print("[调试] placement_by_id timeBar max_value:", time_bar.max_value, " value:", time_bar.value, " visible:", time_bar.visible)
	return result

# 在指示器位置生成放置物
func spawn_at_indicator(id: String, from_player: bool = true, charge_time: float = 0.0, custom_params: Dictionary = {}) -> Node2D:
	var position = Vector2.ZERO

	# 优先用技能指示器
	if Global.has_method("get_indicator_position"):
		position = Global.get_indicator_position()
	elif Global.has_node("/root/PlayerSkills"):
		position = Global.get_node("/root/PlayerSkills").indicator_last_position
	elif Global.has_method("get_player_point_position") and Global.has_method("get_player_facing_direction"):
		var player_pos = Global.get_player_point_position()
		var facing = Global.get_player_facing_direction()
		position = player_pos + facing * 50.0  # 在玩家前方50像素处生成

	if position == Vector2.ZERO:
		push_error("无法确定放置物位置，指示器和玩家位置都无法获取")
		return null

	return place_by_id(id, position, Vector2.RIGHT, from_player, charge_time, custom_params)

# 重新连接电磁幻影的Timer信号，确保它调用release而不是queue_free
func reconnect_ghost_timer(ghost: Node2D) -> void:
	if not is_instance_valid(ghost):
		return
		
	# 获取Timer节点
	var timer = ghost.get_node_or_null("Timer")
	if timer and timer is Timer:
		# 检查是否连接到了queue_free
		if timer.is_connected("timeout", Callable(ghost, "queue_free")):
			# 断开旧连接
			timer.disconnect("timeout", Callable(ghost, "queue_free"))
			
			# 连接到正确的release方法
			if ghost.has_method("release") and not timer.is_connected("timeout", Callable(ghost, "release")):
				timer.connect("timeout", Callable(ghost, "release"))
				
				# 只在debug模式下输出信息，并添加实例ID以区分不同实例
				if OS.is_debug_build():
					var instance_id = ghost.get_instance_id()
					print_debug("已重新连接电磁幻影Timer信号到release方法 - 实例ID: ", instance_id) 

#########################################
# 对象池手动预热和清理 - 公共API
#########################################

# 手动预热对象池 - 供其他脚本调用
func manual_preheat_pools() -> void:
	if pool_is_prewarmed:
		print("对象池已经预热，无需重复操作")
		return
		
	print("手动预热对象池...")
	preheat_object_pools()
	
# 手动清空并重建对象池 - 供其他脚本调用
func manual_reset_pools() -> void:
	print("手动重置对象池...")
	
	# 清空池中所有节点
	clear_object_pools()
	
	# 重新初始化对象池
	initialize_object_pools()
	
	# 立即进行预热而不是等待
	if not pool_is_prewarmed:
		preheat_object_pools()
	
	print("对象池已手动重置并预热")

# 批量预创建并预热幻影 - 在关卡开始时调用
func prepare_ghosts_for_level(count: int = 10) -> void:
	print("为关卡预创建和预热幻影，数量:", count)
	
	# 确保池已初始化
	if electric_ghost_pool.is_empty():
		initialize_object_pools()
	
	# 扩展池大小
	var current_size = electric_ghost_pool.size()
	if current_size < count:
		var to_add = count - current_size
		print("扩展池大小，添加 %d 个新对象" % to_add)
		
		var electric_ghost_scene = load("res://ProjectileFactory/Types/Placement/Electric-ghost.tscn")
		for i in range(to_add):
			var ghost = electric_ghost_scene.instantiate()
			ghost.name = "PooledElectricGhost_level_" + str(current_size + i)
			
			if is_instance_valid(pool_container):
				pool_container.add_child(ghost)
			reconnect_ghost_timer(ghost)
			disable_ghost(ghost)
			
			if "1" in placement_configs:
				var config = placement_configs["1"]
				setup_ghost_with_config(ghost, config)
				
			electric_ghost_pool.append(ghost)
	
	# 强制预热
	pool_is_prewarmed = false
	preheat_object_pools()
	
	print("关卡幻影准备完成，当前池大小:", electric_ghost_pool.size())

# 调试输出池状态
func print_pool_status() -> void:
	print("\n====== 对象池状态 ======")
	print("总池大小: %d" % electric_ghost_pool.size())
	
	var in_use_count = 0
	var invalid_count = 0
	var prewarmed_count = 0
	var failed_textures = 0
	
	for ghost in electric_ghost_pool:
		if not is_instance_valid(ghost):
			invalid_count += 1
			continue
			
		if ghost.get_meta("in_use"):
			in_use_count += 1
			
		# 改进预热状态检查
		if ghost.has_method("get_is_prewarmed") or ghost.get("is_prewarmed") != null:
			# 优先使用方法，如果没有则使用属性
			var is_prewarmed_value = false
			if ghost.has_method("get_is_prewarmed"):
				is_prewarmed_value = ghost.get_is_prewarmed()
			else:
				is_prewarmed_value = ghost.is_prewarmed
				
			if is_prewarmed_value:
				prewarmed_count += 1
			
		# 检查是否有纹理问题
		var has_texture_problem = true
		for part in ["shoulder1", "armor"]:
			var sprite = ghost.get_node_or_null(part)
			if sprite and sprite is Sprite2D and sprite.texture != null:
				has_texture_problem = false
				break
		
		if has_texture_problem:
			failed_textures += 1

	print("使用中: %d" % in_use_count)
	print("无效对象: %d" % invalid_count)
	print("已预热: %d" % prewarmed_count)
	print("纹理问题: %d" % failed_textures)
	print("预热状态: %s" % (str("已完成") if pool_is_prewarmed else str("未完成")))
	print("首次使用: %s" % (str("已完成") if first_use_done else str("未完成")))
	print("活跃放置物: %d" % active_placements.size())
	print("========================\n")

# 检查电磁幻影技能状态变化
func check_electric_ghost_skill(active: bool, level: int) -> void:
	print("[PlacementManagement] 检查电磁幻影技能状态: active=", active, ", level=", level)
	
	# 更新状态变量
	electric_ghost_active = active
	electric_ghost_level = level
	
	# 如果状态变化，可以在这里添加额外处理逻辑
	if active:
		# 确保对象池已经预热
		if !pool_is_prewarmed:
			var texture_info = get_sample_texture_paths()
			force_prewarm_with_data(texture_info)
			print("[PlacementManagement] 已预热电磁幻影对象池")
	
	# 打印当前状态
	print("[PlacementManagement] 电磁幻影技能状态更新: 激活=", electric_ghost_active, ", 等级=", electric_ghost_level)

# 基于ID放置物体，支持自定义参数和敌人使用
func place_by_id(id: String, position: Vector2 = Vector2.ZERO, direction: Vector2 = Vector2.RIGHT, 
				 from_player: bool = true, charge_time: float = 0.0, custom_params: Dictionary = {}) -> Node2D:
	# 检查ID是否存在
	if not placement_configs.has(id):
		print("错误: 放置物ID不存在:", id)
		return null
	
	# 获取配置
	var config = placement_configs[id]
	
	# 打印CSV配置的关键参数，方便追踪
	print("【放置物CSV配置】配置ID:", id, 
		"伤害:", config.get("damage", 10.0),
		"数量:", config.get("projectile_count", 1),
		"生命周期:", config.get("lifetime", 10.0))
	
	# 判断是否是敌人使用技能
	var is_enemy = custom_params.get("is_enemy", false)
	var enemy_instance = custom_params.get("enemy_instance", null)
	
	# 记录额外调试信息
	if is_enemy:
		print("敌人使用放置物技能, ID:", id, "位置:", position, "方向:", direction)
	
	# 获取资源消耗
	var health_cost = config.get("health_cost", 0)
	var mana_cost = config.get("mana_cost", 0)
	var stamina_cost = config.get("stamina_cost", 0)
	
	# 检查玩家资源是否足够 - 只对玩家角色执行
	if from_player and not is_enemy and Global:
		if Global.player_current_health <= health_cost:
			print("生命值不足，无法放置物品")
			var chat_window = get_node_or_null("/root/ChatWindow")
			if chat_window and chat_window.has_method("add_skill_insufficient_message"):
				chat_window.add_skill_insufficient_message("health", "placement")
			return null
		
		if Global.player_current_mana < mana_cost:
			print("魔法值不足，无法放置物品")
			var chat_window = get_node_or_null("/root/ChatWindow")
			if chat_window and chat_window.has_method("add_skill_insufficient_message"):
				chat_window.add_skill_insufficient_message("mana", "placement")
			return null
		
		if Global.player_current_stamina < stamina_cost:
			print("体力不足，无法放置物品")
			var chat_window = get_node_or_null("/root/ChatWindow")
			if chat_window and chat_window.has_method("add_skill_insufficient_message"):
				chat_window.add_skill_insufficient_message("stamina", "placement")
			return null
	
		# 消耗资源 - 只对玩家执行
		if health_cost > 0:
			Global.set_player_health(Global.player_current_health - health_cost)
			print("消耗生命值:", health_cost)
		
		if mana_cost > 0:
			Global.set_player_mana(Global.player_current_mana - mana_cost)
			print("消耗魔法值:", mana_cost)
		
		if stamina_cost > 0:
			Global.set_player_stamina(Global.player_current_stamina - stamina_cost)
			print("消耗体力:", stamina_cost)
	
	# 将player_skills声明移到函数顶部
	var player_skills = get_node_or_null("/root/PlayerSkills")
	var _enemy_skills = get_node_or_null("/root/EnemySkills")
	
	# 获取其他参数
	var base_scale = float(config.get("scale", 1.0))
	var base_count = int(config.get("projectile_count", 1))
	# 确保count初始值始终来自CSV配置，而不受custom_params影响
	var count = base_count
	print("【放置物数量CSV配置】配置ID:", id, "CSV配置放置物数量:", base_count)
	var base_lifetime = float(config.get("lifetime", 10.0))
	var base_speed = float(config.get("speed", 0.0))  # 添加速度参数
	var damage = float(config.get("damage", 10.0))
	var apply_player_attack = config.get("apply_player_attack", true)
	var apply_element_power = config.get("apply_element_power", false)
	
	# 计算角度散布 - 支持武器加成
	var base_angle_spread = float(config.get("angle_spread", 0.0))
	var weapon_angle_spread_bonus = float(custom_params.get("weapon_angle_spread_bonus", 0))
	var final_angle_spread = base_angle_spread + weapon_angle_spread_bonus
	print("【放置物角度散布计算】基础角度散布:", base_angle_spread, "武器加成:", weapon_angle_spread_bonus, "最终角度散布:", final_angle_spread)
	
	# 放置物的缩放仅使用基础值，不受蓄气影响
	var final_scale = base_scale
	print("【放置物缩放】使用基础缩放值: ", final_scale)
	
	# 计算放置物生命周期 - 根据蓄气时间增加
	var lifetime = base_lifetime
	if charge_time > 0:  # 无论是玩家还是敌人，都应用蓄气时间增加生命周期的逻辑
		# 放置物的生命周期随蓄气时间增加，倍数为2倍
		var lifetime_bonus = charge_time * 2.0
		lifetime = base_lifetime + lifetime_bonus
		print("放置物生命周期: 基础=", base_lifetime, "蓄气加成=", lifetime_bonus, "最终=", lifetime)
		
		# 如果使用了能量压缩，需要还原原始蓄气时间计算生命周期
		if player_skills and from_player:
			var compression_level = player_skills.current_levels.get("energy_compression", 0)
			if compression_level > 0:
				var compression_value_per_level = player_skills.passive_data.get("energy_compression", {}).get("value_per_level", 0.1)
				var compression_reduction = compression_level * compression_value_per_level
				if compression_reduction > 0:
					# 反向计算原始时间
					var original_charge_time = charge_time / (1.0 - compression_reduction)
					# 使用原始时间重新计算生命周期加成
					lifetime_bonus = original_charge_time * 2.0
					lifetime = base_lifetime + lifetime_bonus
					print("使用原始蓄气时间重新计算生命周期: 基础=", base_lifetime, "蓄气加成=", lifetime_bonus, "最终=", lifetime)

	# 计算放置物速度
	var speed = base_speed
	if custom_params.has("speed"):
		speed = float(custom_params["speed"])
	print("【放置物速度】基础速度:", base_speed, "自定义速度:", speed)

	# 移除is_enemy和玩家分支中所有修改final_scale的逻辑
	# final_scale 将始终保持为 base_scale
	
	# 计算玩家蓄气时的放置物数量
	if from_player and charge_time > 0 and not custom_params.has("projectile_count") and player_skills:
		# 使用PlayerSkills获取蓄气段数信息
		var segment_time = player_skills.get_charge_time() / player_skills.get_charge_segments()
		var current_segment = int(charge_time / segment_time)
		
		# 增加放置物数量：基础数量 + 蓄气段数
		count = base_count + current_segment
		print("放置物数量: 基础=", base_count, "蓄气段数=", current_segment, "最终=", count)
	# 如果custom_params有明确指定的projectile_count，则使用它覆盖（仅在这种情况下）
	elif custom_params.has("projectile_count"):
		count = int(custom_params.get("projectile_count"))
		print("放置物数量: 使用自定义参数指定的数量:", count)
	
	# 获取基础伤害值
	var final_damage = damage
	
	if is_enemy:
		# 敌人伤害计算
		if enemy_instance:
			# 如果有敌人实例，尝试使用敌人的属性
			var enemy_attack = 0
			var enemy_element = 0
			
			# 尝试获取敌人的攻击力和元素力
			if enemy_instance.has_method("get_attack_power"):
				enemy_attack = enemy_instance.get_attack_power()
			elif "attack_power" in enemy_instance:
				enemy_attack = enemy_instance.attack_power
			else:
				# 默认敌人攻击力 - 放置物类型的敌人通常有很高的元素攻击
				enemy_attack = 8
			
			if enemy_instance.has_method("get_element_power"):
				enemy_element = enemy_instance.get_element_power()
			elif "element_power" in enemy_instance:
				enemy_element = enemy_instance.element_power
			else:
				# 默认敌人元素力 - 放置物类型敌人通常有高元素力
				enemy_element = 12
			
			# 根据敌人等级/难度加成
			var enemy_level = 1
			if "level" in enemy_instance:
				enemy_level = enemy_instance.level
			elif enemy_instance.has_method("get_level"):
				enemy_level = enemy_instance.get_level()
			
			# 计算最终伤害
			final_damage = damage
			
			# 应用攻击力 - 放置物较少依赖物理攻击
			if apply_player_attack:
				final_damage += enemy_attack * 0.7
			
			# 应用元素力 - 放置物非常依赖元素
			if apply_element_power and config.groups.strip_edges() != "":
				var element_multiplier = 1.2  # 放置物默认元素倍率高
				final_damage += enemy_element * element_multiplier
			
			# 应用蓄气加成
			if charge_time > 0:
				var charge_multiplier = 1.0 + charge_time * 0.7  # 每秒增加70%伤害
				final_damage *= charge_multiplier
			
			# 应用敌人等级加成
			final_damage *= (1.0 + (enemy_level - 1) * 0.18)  # 每级增加18%伤害
			
			print("敌人放置物最终伤害:", final_damage, "基础伤害:", damage, 
				"敌人攻击:", enemy_attack, "敌人元素:", enemy_element, 
				"蓄气时间:", charge_time, "敌人等级:", enemy_level)
		else:
			# 没有敌人实例时的简单伤害计算
			final_damage = damage * 1.3  # 放置物基础伤害加成
			
			# 简单蓄气加成
			if charge_time > 0:
				final_damage *= (1.0 + charge_time * 0.7)
				
			print("简化敌人放置物伤害计算:", final_damage, "基础伤害:", damage, "蓄气时间:", charge_time)
	elif from_player:
		# 玩家伤害计算
		if player_skills:
			# 使用字符串"PLACEMENT"代替枚举值，避免访问属性错误
			var manager_type_placement = 3  # 假设PLACEMENT的枚举值是3，如果不是请相应调整
			
			final_damage = player_skills.calculate_damage(
				damage, 
				manager_type_placement,  # 使用数值代替枚举引用
				config.groups,
				apply_player_attack,
				apply_element_power
			)
			
			# 应用蓄气伤害加成
			if charge_time > 0:
				var pre_charge_damage = final_damage
				
				# 关键修改：使用原始未压缩的时间计算伤害
				var original_charge_time = charge_time
				
				# 如果使用了能量压缩，需要还原原始蓄气时间
				var compression_level = player_skills.current_levels.get("energy_compression", 0)
				if compression_level > 0:
					var compression_value_per_level = player_skills.passive_data.get("energy_compression", {}).get("value_per_level", 0.1)
					var compression_reduction = compression_level * compression_value_per_level
					if compression_reduction > 0:
						# 反向计算原始时间：原始时间 = 压缩时间 / (1 - 压缩比例)
						original_charge_time = charge_time / (1.0 - compression_reduction)
						print("还原原始蓄气时间: 压缩后=", charge_time, ", 原始=", original_charge_time, ", 压缩比例=", compression_reduction)
				
				# 使用原始蓄气时间计算伤害
				final_damage = player_skills.calculate_charge_damage(final_damage, original_charge_time)
				print("放置物蓄气伤害计算: 前=", pre_charge_damage, ", 后=", final_damage, 
					  ", 增加=", final_damage - pre_charge_damage, 
					  " (蓄气时间:", original_charge_time, "秒)")
		else:
			# 如果找不到PlayerSkills，使用简单算法
			if apply_player_attack:
				final_damage += Global.player_attack_power * 0.7
			
			if apply_element_power and config.groups.strip_edges() != "":
				var element_bonus = Global.player_element_power
				var element_multiplier = 1.2  # 放置物默认元素倍率高
				final_damage += element_bonus * element_multiplier
				
			# 使用简单的蓄气伤害计算
			if charge_time > 0:
				var damage_bonus_percent = charge_time * 100.0  # 每0.1秒增加10%
				final_damage = final_damage * (1 + damage_bonus_percent / 100.0)
	
	print("放置物最终伤害:", final_damage, "基础伤害:", damage)

	# 应用武器强化加成
	var skills_system = get_node_or_null("/root/PlayerSkills")
	if skills_system and skills_system.has_method("get_weapon_enhancement_bonus"):
		# 应用伤害强化（固定值增加）
		var damage_bonus = skills_system.get_weapon_enhancement_bonus("damage")
		if damage_bonus > 0:
			final_damage += damage_bonus
			print("[放置物管理] 应用伤害强化: +%s点伤害" % damage_bonus)

		# 应用大小强化
		var scale_bonus = skills_system.get_weapon_enhancement_bonus("scale")
		if scale_bonus > 0:
			final_scale += final_scale * scale_bonus
			print("[放置物管理] 应用大小强化: +%s%%" % (scale_bonus * 100))

		# 应用数量强化
		var count_bonus = skills_system.get_weapon_enhancement_bonus("count")
		if count_bonus > 0:
			count += count_bonus
			print("[放置物管理] 应用数量强化: +%d" % count_bonus)

		# 应用生命周期强化
		var lifetime_bonus = skills_system.get_weapon_enhancement_bonus("lifetime")
		if lifetime_bonus > 0:
			lifetime += lifetime * lifetime_bonus
			print("[放置物管理] 应用生命周期强化: +%s%%" % (lifetime_bonus * 100))

		# 应用速度强化（固定值增加）
		var speed_bonus = skills_system.get_weapon_enhancement_bonus("speed")
		if speed_bonus > 0 and speed > 0:  # 只对有速度的放置物应用速度强化
			speed += speed_bonus
			print("[放置物管理] 应用速度强化: +%s点速度" % speed_bonus)
		elif speed_bonus > 0 and speed == 0:
			print("[放置物管理] 速度强化可用，但当前放置物速度为0，跳过")

	# 更新自定义参数中的伤害值
	var merged_params = custom_params.duplicate()
	merged_params["damage"] = final_damage
	merged_params["lifetime"] = lifetime  # 使用计算后的生命周期
	merged_params["speed"] = speed  # 使用计算后的速度
	merged_params["scale"] = final_scale
	merged_params["angle_spread"] = final_angle_spread  # 传递计算后的角度散布
	merged_params["groups"] = config.groups
	merged_params["placement_id"] = id
	merged_params["apply_player_attack"] = apply_player_attack
	merged_params["apply_element_power"] = apply_element_power
	
	# 重要：将最终的放置物数量添加到merged_params
	merged_params["projectile_count"] = count
	print("【放置物数量最终值】merged_params.projectile_count =", merged_params.projectile_count)
	
	print("【放置物数量检查】最终使用的projectile_count =", merged_params.projectile_count, 
		"来源:", str("custom_params") if custom_params.has("projectile_count") else str("CSV配置+蓄气"))
	
	print("【放置物数量实际使用】实际使用的projectile_count =", count, "将生成", count, "个放置物")
	
	# 获取场景资源
	var scene_resource = config.get("scene_resource", null)
	if not scene_resource:
		push_error("错误: 放置物场景资源未指定")
		return null
	
	# 获取放置物实例
	var placement_instance = null
	
	if count > 1:
		# 生成多个放置物
		print("生成多个放置物: 数量=", count)
		# 注意：返回的是最后一个实例，可能需要修改返回一个数组
		placement_instance = spawn_multiple_placement(position, direction, count, from_player, merged_params, scene_resource)
	else:
		# 生成单个放置物
		placement_instance = spawn_single_placement(position, direction, from_player, merged_params, scene_resource)
	
	# 确保缩放正确应用
	if placement_instance:
		print("放置物生成前缩放:", placement_instance.scale)
		placement_instance.scale = Vector2(final_scale, final_scale)
		print("放置物生成后缩放:", placement_instance.scale)
		
		# 检查show_ui参数，如果有放置物ID，应用show_ui设置
		if "placement_id" in merged_params and placement_configs.has(merged_params["placement_id"]):
			var placement_config = placement_configs[merged_params["placement_id"]]
			if placement_config.has("show_ui"):
				var time_bar = placement_instance.get_node_or_null("timeBar")
				if time_bar:
					time_bar.visible = placement_config["show_ui"]  # 当show_ui为true时显示，为false时隐藏
		
		# 使用计时器延迟检查
		var check_timer = Timer.new()
		check_timer.one_shot = true
		check_timer.wait_time = 0.1
		placement_instance.add_child(check_timer)
		check_timer.timeout.connect(func():
			if is_instance_valid(placement_instance):
				print("延迟后放置物缩放:", placement_instance.scale)
			check_timer.queue_free()
		)
		check_timer.start()
	
	return placement_instance

# 延迟设置投射物攻击组ID的方法
func _set_projectile_attack_group_id(projectile_box: Node, custom_params: Dictionary):
	if not projectile_box or not is_instance_valid(projectile_box):
		return
	
	# 为放置物设置攻击组ID
	if has_node("/root/攻击管理器"):
		var attack_group_manager = get_node("/root/攻击管理器")
		var current_group_id = attack_group_manager.get_current_attack_group_id()
		
		# 如果武器系统还没有创建攻击组，或者这是独立的放置物攻击，创建新的攻击组
		if current_group_id == "" or custom_params.get("independent_attack", false):
			current_group_id = attack_group_manager.generate_new_attack_group_id()
			attack_group_manager.set_attack_group_id_for_system("放置物系统", current_group_id)
		else:
			# 使用现有的攻击组ID（与武器系统协调）
			attack_group_manager.set_attack_group_id_for_system("放置物系统", current_group_id)
		
		# 设置放置物的攻击组ID
		if projectile_box.has_method("set_attack_group_id"):
			projectile_box.set_attack_group_id(current_group_id)
		
		# 注册HitBox到攻击管理器
		attack_group_manager.register_hitbox_to_group(current_group_id, projectile_box)
		
		print("[放置物管理] 延迟设置放置物攻击组ID: ", current_group_id)

# 检查和消耗资源的辅助函数
func check_and_consume_resources(config: Dictionary) -> bool:
	if not Global:
		return false
		
	var health_cost = config.get("health_cost", 0)
	var mana_cost = config.get("mana_cost", 0)
	var stamina_cost = config.get("stamina_cost", 0)
	
	# 检查资源是否足够
	if Global.player_current_health <= health_cost:
		return false
	if Global.player_current_mana < mana_cost:
		return false
	if Global.player_current_stamina < stamina_cost:
		return false
	
	# 消耗资源
	var _resource_consumed = false
	if health_cost > 0:
		Global.set_player_health(Global.player_current_health - health_cost)
		_resource_consumed = true
	if mana_cost > 0:
		Global.set_player_mana(Global.player_current_mana - mana_cost)
		_resource_consumed = true
	if stamina_cost > 0:
		Global.set_player_stamina(Global.player_current_stamina - stamina_cost)
		_resource_consumed = true
	
	return true

# 生成单个放置物 - 供place_by_id调用
func spawn_single_placement(position: Vector2, direction: Vector2, from_player: bool, params: Dictionary, scene_resource: Resource) -> Node2D:
	if debug_mode:
		print("[PlacementManagement] 生成单个放置物，位置:", position, "方向:", direction)
	
	# 确保params字典中包含场景资源
	var merged_params = params.duplicate()
	merged_params["scene"] = scene_resource
	
	# 直接使用spawn_placement函数创建放置物
	var placement = spawn_placement(position, direction, from_player, false, merged_params)
	
	# 返回创建的放置物实例
	return placement

# 生成多个放置物 - 供place_by_id调用
func spawn_multiple_placement(position: Vector2, direction: Vector2, count: int, from_player: bool, params: Dictionary, scene_resource: Resource) -> Node2D:
	if debug_mode:
		print("[PlacementManagement] 生成多个放置物，数量:", count, "位置:", position, "方向:", direction)
	
	# 确保params字典中包含场景资源
	var merged_params = params.duplicate()
	merged_params["scene"] = scene_resource
	
	# 保存创建的放置物列表
	var placements = []
	var result_placement = null
	
	# 计算放置物之间的角度间隔 - 从参数中获取角度散布
	var angle_spread_degrees = params.get("angle_spread", 30.0)  # 从参数获取角度散布，默认30度
	var angle_spread = deg_to_rad(angle_spread_degrees)
	var angle_step = angle_spread / (count - 1) if count > 1 else 0.0
	var start_angle = -angle_spread / 2
	print("【多个放置物角度散布】使用角度散布:", angle_spread_degrees, "度")
	
	# 获取基准角度
	var base_angle = 0
	if direction.x != 0 or direction.y != 0:
		base_angle = atan2(direction.y, direction.x)
	
	# 生成每个放置物
	for i in range(count):
		# 计算当前角度
		var current_angle = base_angle + start_angle + angle_step * i
		
		# 创建方向向量
		var current_direction = Vector2(cos(current_angle), sin(current_angle))
		
		# 计算偏移位置 - 在放置物之间留一定距离
		var offset_position = position + current_direction * (20.0 * i)
		
		# 生成放置物
		var placement = spawn_placement(offset_position, current_direction, from_player, false, merged_params)
		
		if placement:
			# 检查show_ui参数，如果有放置物ID，应用show_ui设置
			if "placement_id" in merged_params and placement_configs.has(merged_params["placement_id"]):
				var placement_config = placement_configs[merged_params["placement_id"]]
				if placement_config.has("show_ui"):
					var time_bar = placement.get_node_or_null("timeBar")
					if time_bar:
						time_bar.visible = placement_config["show_ui"]  # 当show_ui为true时显示，为false时隐藏
			
			placements.append(placement)
			# 保存第一个创建的放置物作为返回值
			if result_placement == null:
				result_placement = placement
				
	# 如果没有成功创建任何放置物，返回null
	if placements.is_empty():
		return null
		
	# 返回第一个放置物作为代表
	return result_placement

# 不消耗资源的放置物生成版本 - 用于PlayerSkills中先检查能否生成，成功后再消耗资源
func spawn_at_indicator_no_consume(id: String, from_player: bool = true, charge_time: float = 0.0, custom_params: Dictionary = {}) -> Node2D:
	# 检查ID是否存在
	if not placement_configs.has(id):
		print("错误: 放置物ID不存在:", id)
		return null

	# 获取配置
	var config = placement_configs[id]

	# 临时将资源消耗设置为0，这样原始方法就不会消耗资源
	var temp_config = config.duplicate()
	temp_config["health_cost"] = 0
	temp_config["mana_cost"] = 0
	temp_config["stamina_cost"] = 0

	# 临时替换配置
	placement_configs[id] = temp_config

	# 调用原始方法
	var result = spawn_at_indicator(id, from_player, charge_time, custom_params)

	# 恢复原始配置
	placement_configs[id] = config

	return result

# 不消耗资源的基于ID放置物体版本
func place_by_id_no_consume(id: String, position: Vector2 = Vector2.ZERO, direction: Vector2 = Vector2.RIGHT,
							from_player: bool = true, charge_time: float = 0.0, custom_params: Dictionary = {}) -> Node2D:
	# 检查ID是否存在
	if not placement_configs.has(id):
		print("错误: 放置物ID不存在:", id)
		return null

	# 获取配置
	var config = placement_configs[id]

	# 判断是否是敌人使用技能
	var is_enemy = custom_params.get("is_enemy", false)

	# 获取资源消耗
	var health_cost = config.get("health_cost", 0)
	var mana_cost = config.get("mana_cost", 0)
	var stamina_cost = config.get("stamina_cost", 0)

	# 只检查玩家资源是否足够，不消耗 - 只对玩家角色执行
	if from_player and not is_enemy and Global:
		if Global.player_current_health <= health_cost:
			print("生命值不足，无法创建放置物")
			var chat_window = get_node_or_null("/root/ChatWindow")
			if chat_window and chat_window.has_method("add_skill_insufficient_message"):
				chat_window.add_skill_insufficient_message("health", "placement")
			return null

		if Global.player_current_mana < mana_cost:
			print("魔法值不足，无法创建放置物")
			var chat_window = get_node_or_null("/root/ChatWindow")
			if chat_window and chat_window.has_method("add_skill_insufficient_message"):
				chat_window.add_skill_insufficient_message("mana", "placement")
			return null

		if Global.player_current_stamina < stamina_cost:
			print("体力不足，无法创建放置物")
			var chat_window = get_node_or_null("/root/ChatWindow")
			if chat_window and chat_window.has_method("add_skill_insufficient_message"):
				chat_window.add_skill_insufficient_message("stamina", "placement")
			return null

	# 注意：这里不消耗资源，直接生成放置物
	# 使用更简单的方法：临时修改配置中的资源消耗值

	# 重要：直接调用原始的place_by_id方法，但跳过资源消耗部分
	# 这样可以确保所有的配置参数都被正确处理

	# 临时将资源消耗设置为0，这样原始方法就不会消耗资源
	var temp_config = config.duplicate()
	temp_config["health_cost"] = 0
	temp_config["mana_cost"] = 0
	temp_config["stamina_cost"] = 0

	# 临时替换配置
	placement_configs[id] = temp_config

	# 调用原始方法
	var result = place_by_id(id, position, direction, from_player, charge_time, custom_params)

	# 恢复原始配置
	placement_configs[id] = config

	return result
