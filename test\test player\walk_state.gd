extends LimboState

@export var animation_tree: AnimationTree
@export var walk_state_name: StringName = "Walk"
@export var idle_transition_delay: float = 0.2  # 从 0.6 改为 0.2
@export var blend_transition_delay: float = 0.05  # 改为 0.05 秒
@export var dash_duration: float = 0.3           # 冲刺持续时间，单位秒
@export var dash_speed_multiplier: float = 2.0     # 冲刺时的速度倍率
@export var dash_speed_bonus: float = 100.0       # 冲刺时的额外速度

var _idle_transition_timer: float = 0.0  # 切换到 Idle 状态的计时器
var _blend_transition_timer: float = 0.0  # blend_position 切换的计时器
var _target_blend_position: float = 0.0  # 目标 blend_position
var _current_blend_position: float = 0.0  # 当前 blend_position
var _buffered_input: String = ""  # 预输入缓存
var _recovery_timer: Timer = null   # 新增：体力恢复计时器
var is_dashing: bool = false                       # 标记是否冲刺中
var dash_timer: float = 0.0                        # 冲刺剩余时间
var dash_ready: bool = true                        # 用于防止重复处理 Dash 输入
var _stamina_check_timer: float = 0.0              # 用于检测体力变化的计时器
var _last_stamina: float = 0.0                         # 上次记录的体力值
var ghost_timer: float = 0.0  # 添加幻影计时器
var Electric_ghost_level: int = 0  # 添加实质幻影技能等级
var Electric_ghost_max_level: int = 5  # 添加实质幻影技能最大等级
var footstep_audio: AudioStreamPlayer2D = null  # 脚步声播放器

# ==================== 多线程GPU加速相关变量 ====================
# 多线程优化相关变量
var _worker_thread_pool_enabled: bool = true
var _thread_pool_tasks: Array = []
var _max_concurrent_tasks: int = 4
var _current_task_count: int = 0
var _thread_mutex: Mutex = Mutex.new()

# GPU计算着色器相关（如果支持）
var _compute_shader_enabled: bool = false
var _rendering_device: RenderingDevice = null
var _compute_shader: RID
var _gpu_buffer: RID

# 幻影设置优化
var _gpu_acceleration_enabled: bool = false
var _ghost_setup_cache: Dictionary = {}
var _texture_preload_cache: Dictionary = {}

# 性能监控
var _ghost_setup_time: float = 0.0
var _gpu_processing_time: float = 0.0
var _cpu_processing_time: float = 0.0

func _enter() -> void:
	animation_tree["parameters/playback"].travel(walk_state_name)  # 切换到 Walk 动画状态
	_idle_transition_timer = 0.0  # 重置计时器
	_blend_transition_timer = 0.0  # 重置 blend_position 计时器
	_target_blend_position = 0.0  # 重置目标 blend_position
	_current_blend_position = 0.0  # 重置当前 blend_position
	_buffered_input = ""  # 重置预输入缓存

	# 检查角色是否处于预览模式
	var knight = get_parent().get_parent()
	if knight.is_in_group("CharacterPreview"):
		# 在预览模式下不执行实际的游戏逻辑
		return

	# 初始化多线程GPU加速系统
	_initialize_gpu_acceleration()

	_last_stamina = Global.player_current_stamina  # 初始化上次记录的体力值
	_stamina_check_timer = 0.0  # 重置体力检测计时器

	# 获取实质幻影技能等级
	Electric_ghost_level = 0  # 默认为0
	var skill_ui = get_node_or_null("/root/技能加点界面")
	if skill_ui and skill_ui.has_method("get_skill_level"):
		Electric_ghost_level = skill_ui.get_skill_level("Electric_Ghost")

		# 初始化技能最大等级
		if skill_ui.has_method("get_skill_max_level"):
			Electric_ghost_max_level = skill_ui.get_skill_max_level("Electric_Ghost")
		else:
			Electric_ghost_max_level = 5  # 默认值

	# 设置动画速度与移动速度相关
	var speed_ratio = Global.player_speed / 400.0
	animation_tree.set("parameters/Walk/TimeScale/scale", speed_ratio)

	# 检查技能并提前预热实质幻影对象池
	var player_skills = get_node_or_null("/root/PlayerSkills")
	if player_skills and player_skills.get_skill_level("Electric_Ghost") > 0:
		# 如果有实质幻影技能，立即预热对象池
		pre_warm_electric_ghost_pool()

	# 预加载和缓存玩家纹理数据
	_preload_player_textures(knight)

	# 获取脚步声播放器
	footstep_audio = knight.get_node_or_null("玩家脚步")

func _update(delta: float) -> void:
	var knight = get_parent().get_parent()
	
	# 检查角色是否处于预览模式
	if knight.is_in_group("CharacterPreview"):
		# 在预览模式下简单显示Walk动画，不处理输入和移动
		if animation_tree:
			animation_tree.set("parameters/Walk/Walk/blend_position", 0)
		return
		
	var velocity = Vector2.ZERO
	
	# 检查玩家是否可以移动
	if not Global.get_player_movement():
		# 如果不能移动，停止所有移动并保持当前动画状态
		knight.velocity = Vector2.ZERO
		knight.move_and_slide()
		return
	
	# 立即更新方向和动画参数
	if Input.is_action_pressed("Right"):
		velocity.x += 1
		knight.set_blend_position(1)  # 直接设置，不使用中间变量
		# 直接设置动画树参数，确保立即更新
		animation_tree.set("parameters/Walk/Walk/blend_position", 1.0)
	elif Input.is_action_pressed("Left"):
		velocity.x -= 1
		knight.set_blend_position(-1)  # 直接设置，不使用中间变量
		# 直接设置动画树参数，确保立即更新
		animation_tree.set("parameters/Walk/Walk/blend_position", -1.0)
	if Input.is_action_pressed("Up"):
		velocity.y -= 1
	if Input.is_action_pressed("Down"):
		velocity.y += 1

	# 当 Dash 按键释放时重置 dash_ready，确保只处理一次 Dash 输入
	if not Input.is_action_pressed("Dash"):
		dash_ready = true
	
	# 处理 Dash 输入（仅在 dash_ready 为 true 时响应）
	if dash_ready and Input.is_action_just_pressed("Dash") and not is_dashing:
		# 检查当前武器类型
		var paper_doll_system = get_node("/root/PaperDollSystem")
		var weapon_type = paper_doll_system.get_current_weapon_type()
		
		# 如果是法师法杖类武器，进入hover状态
		if weapon_type == PaperDollSystem.WeaponType.STAFF:
			get_root().dispatch("hover!")
			return  # 直接返回，不继续执行后面的dash逻辑
		
		# 首先检查全局中是否允许冲刺
		if not Global.is_dash_enabled():
			dash_ready = false
			return
		
		# 计算消耗 - 检查实质幻影技能等级
		var stamina_cost = 3  # 默认体力消耗
		var mana_cost = 0     # 默认不消耗魔法
		
		# 如果实质幻影技能激活，修改消耗
		if Electric_ghost_level > 0:
			stamina_cost = 5 + (Electric_ghost_level * 5)  # 基础5点体力 + 每级5点
			mana_cost = 5 + (Electric_ghost_level * 5)     # 基础5点魔法 + 每级5点
		
		# 检查资源是否足够
		if Global.player_current_stamina >= stamina_cost and Global.player_current_mana >= mana_cost:
			# 开始冲刺
			is_dashing = true
			dash_timer = dash_duration
			animation_tree["parameters/playback"].travel("Dash")
			
			# 设置 Dash 动画的 blend_position
			if velocity.x > 0:
				animation_tree.set("parameters/Dash/BlendSpace1D/blend_position", 1.0)
			elif velocity.x < 0:
				animation_tree.set("parameters/Dash/BlendSpace1D/blend_position", -1.0)

			# 设置玩家不可受伤
			knight.can_take_damage = false

			# 消耗资源
			Global.player_current_stamina = Global.player_current_stamina - stamina_cost
			Global.emit_signal("stamina_changed", Global.player_current_stamina)
			
			# 如果需要消耗魔法，扣除魔法值
			if mana_cost > 0:
				Global.player_current_mana = Global.player_current_mana - mana_cost
				Global.emit_signal("mana_changed", Global.player_current_mana)
			
			# 创建幻影 - 仅在冲刺开始时创建一次
			create_dash_ghost(knight)
			
			dash_ready = false

			# 播放 Dash 音效
			if knight.has_node("Dash"):
				knight.get_node("Dash").play()
		else:
			# 资源不足，显示提示消息
			if Global.player_current_stamina < stamina_cost:
				# 发送战斗消息而不是系统消息
				var chat_window = get_node_or_null("/root/ChatWindow")
				if chat_window and chat_window.has_method("add_skill_insufficient_message"):
					chat_window.add_skill_insufficient_message("stamina", "sprint")
			elif mana_cost > 0 and Global.player_current_mana < mana_cost:
				# 发送战斗消息而不是系统消息
				var chat_window = get_node_or_null("/root/ChatWindow")
				if chat_window and chat_window.has_method("add_skill_insufficient_message"):
					chat_window.add_skill_insufficient_message("mana", "phantom")
			
			dash_ready = false

	# 归一化速度向量以保持恒定速度
	if is_dashing:
		velocity = velocity.normalized() * (knight.speed + dash_speed_bonus) * dash_speed_multiplier
	else:
		velocity = velocity.normalized() * knight.speed
	knight.velocity = velocity
	knight.move_and_slide()

	# 冲刺逻辑更新
	if is_dashing:
		dash_timer -= delta
		ghost_timer += delta
		
		# 降低幻影生成频率，从原来的0.05秒一次提高到0.1秒一次
		if ghost_timer >= 0.1:
			create_dash_ghost(knight)
			ghost_timer = 0.0
		
		if dash_timer <= 0:
			is_dashing = false
			knight.can_take_damage = true
			animation_tree["parameters/playback"].travel(walk_state_name)  # 切回 Walk 动画
			# 如果体力为 0，则强制进入 Idle 状态
			if Global.player_current_stamina <= 0:
				knight.velocity = Vector2.ZERO
				get_root().dispatch("stop!")
			else:
				# 否则保持 Walk 状态，允许连续 dash
				pass

	# 简化移动检测逻辑
	if velocity == Vector2.ZERO:
		_idle_transition_timer += delta
		if _idle_transition_timer >= idle_transition_delay:
			get_root().dispatch("stop!")
		# 停止移动时停止脚步声
		if footstep_audio and footstep_audio.playing:
			footstep_audio.stop()
	else:
		_idle_transition_timer = 0.0
		# 移动时播放脚步声（循环播放）
		if footstep_audio and not footstep_audio.playing and not is_dashing:
			footstep_audio.play()

	# 只精简分发攻击事件部分
	if Input.is_action_pressed("Attack"):
		var paper_doll_system = get_node("/root/PaperDollSystem")
		var weapon_type = paper_doll_system.get_current_weapon_type()
		
		# 先检查是否允许挥砍移动
		if Global.is_loop_attacking:
			# 检查体力是否足够
			if knight.has_method("has_enough_stamina_for_loop_attack") and not knight.has_enough_stamina_for_loop_attack():
				# 体力不足，转为普通攻击
				knight.velocity = Vector2.ZERO
				get_root().dispatch("normal_attack!", "from_walk")
				return
				
			# 只有允许挥砍移动时才执行原有的转换逻辑
			if weapon_type == PaperDollSystem.WeaponType.BOW:
				get_root().dispatch("remote_attack!")
			else:
				# 原代码可能在这里分发了loop_attack事件
				get_root().dispatch("loop_attack!")
		else:
			# 禁止挥砍移动状态 - 直接停止移动并转向普通攻击
			knight.velocity = Vector2.ZERO
			# 使用特殊参数通知attack_state这是从walk_state转来的
			get_root().dispatch("normal_attack!", "from_walk")
		
		return
	elif Input.is_action_just_pressed("charging"):
		get_root().dispatch("charging!")

	# 检查体力变化
	if Global.player_current_stamina != _last_stamina:
		_stamina_check_timer = 0.0  # 重置计时器
		_last_stamina = Global.player_current_stamina
	else:
		_stamina_check_timer += delta
		if _stamina_check_timer >= 1.0:
			_start_stamina_recovery()
			_stamina_check_timer = 0.0

# ==================== 多线程GPU加速系统初始化 ====================
# 初始化多线程GPU加速系统
func _initialize_gpu_acceleration() -> void:
	# 检测CPU核心数并设置线程池大小
	var cpu_count: int = OS.get_processor_count()
	_max_concurrent_tasks = max(2, min(cpu_count - 1, 4))  # 保留核心给主线程，限制最大4个任务

	# 尝试初始化GPU计算支持
	_initialize_gpu_compute()

	# 预热线程池
	_preheat_thread_pool()

	print("[WalkState] 多线程GPU加速系统初始化完成 - CPU核心: %d, 最大并发任务: %d, GPU加速: %s" % [cpu_count, _max_concurrent_tasks, _gpu_acceleration_enabled])

# 初始化GPU计算着色器支持
func _initialize_gpu_compute() -> void:
	# 检查是否支持计算着色器（需要Forward+或Mobile渲染器）
	var rendering_method = ProjectSettings.get_setting("rendering/renderer/rendering_method", "")
	if rendering_method in ["forward_plus", "mobile"]:
		_rendering_device = RenderingServer.create_local_rendering_device()
		if _rendering_device:
			_compute_shader_enabled = true
			_gpu_acceleration_enabled = true
			print("[WalkState] GPU计算着色器支持已启用")
		else:
			print("[WalkState] GPU计算着色器初始化失败，使用CPU处理")
	else:
		print("[WalkState] 当前渲染器不支持计算着色器: %s" % rendering_method)

# 预热线程池
func _preheat_thread_pool() -> void:
	if not _worker_thread_pool_enabled:
		return

	# 创建一些轻量级任务来预热线程池
	for i in range(_max_concurrent_tasks):
		var task_id = WorkerThreadPool.add_task(_dummy_thread_task.bind(i))
		_thread_pool_tasks.append(task_id)

	# 等待预热任务完成
	await get_tree().create_timer(0.05).timeout

	# 清理预热任务
	for task_id in _thread_pool_tasks:
		if WorkerThreadPool.is_task_completed(task_id):
			WorkerThreadPool.wait_for_task_completion(task_id)
	_thread_pool_tasks.clear()
	_current_task_count = 0

# 虚拟线程任务用于预热
func _dummy_thread_task(_task_id: int) -> float:
	# 简单的计算任务来预热线程
	var result: float = 0.0
	for i in range(500):
		result += sin(i * 0.01)
	return result

# 预加载玩家纹理数据
func _preload_player_textures(knight: Node2D) -> void:
	if not is_instance_valid(knight):
		return

	var start_time = Time.get_ticks_msec()

	# 使用多线程预加载纹理
	if _worker_thread_pool_enabled and _current_task_count < _max_concurrent_tasks:
		_thread_mutex.lock()
		_current_task_count += 1
		_thread_mutex.unlock()

		var task_id = WorkerThreadPool.add_task(_preload_textures_task.bind(knight))
		_thread_pool_tasks.append(task_id)
	else:
		# 单线程预加载
		_preload_textures_sync(knight)

	var end_time = Time.get_ticks_msec()
	print("[WalkState] 纹理预加载完成，耗时: %.3f秒" % ((end_time - start_time) / 1000.0))

# 多线程纹理预加载任务
func _preload_textures_task(knight: Node2D) -> Dictionary:
	var result = {
		"success": false,
		"texture_count": 0,
		"method": "多线程"
	}

	# 在工作线程中准备纹理数据
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	var texture_data = {}

	for node_name in node_names:
		var node = knight.get_node_or_null(node_name)
		if node and node is Sprite2D and node.texture:
			var texture_path = node.texture.resource_path
			if texture_path and not texture_path.is_empty():
				texture_data[node_name] = {
					"path": texture_path,
					"flip_h": node.flip_h,
					"frame": node.frame,
					"hframes": node.hframes,
					"vframes": node.vframes,
					"visible": node.visible,
					"position": node.position,
					"rotation": node.rotation,
					"scale": node.scale,
					"modulate": node.modulate,
					"z_index": node.z_index
				}
				result.texture_count += 1

	# 在主线程中缓存结果
	call_deferred("_cache_texture_data", texture_data)

	_thread_mutex.lock()
	_current_task_count -= 1
	_thread_mutex.unlock()

	result.success = true
	return result

# 同步纹理预加载
func _preload_textures_sync(knight: Node2D) -> void:
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	var texture_data = {}

	for node_name in node_names:
		var node = knight.get_node_or_null(node_name)
		if node and node is Sprite2D and node.texture:
			var texture_path = node.texture.resource_path
			if texture_path and not texture_path.is_empty():
				texture_data[node_name] = {
					"path": texture_path,
					"flip_h": node.flip_h,
					"frame": node.frame,
					"hframes": node.hframes,
					"vframes": node.vframes,
					"visible": node.visible,
					"position": node.position,
					"rotation": node.rotation,
					"scale": node.scale,
					"modulate": node.modulate,
					"z_index": node.z_index
				}

	_cache_texture_data(texture_data)

# 缓存纹理数据（主线程调用）
func _cache_texture_data(texture_data: Dictionary) -> void:
	_texture_preload_cache = texture_data
	print("[WalkState] 已缓存 %d 个纹理数据" % texture_data.size())

func _exit() -> void:
	# 检查角色是否处于预览模式
	var knight = get_parent().get_parent()
	if knight.is_in_group("CharacterPreview"):
		# 预览模式下不需要停止体力恢复
		return

	# 等待所有线程任务完成
	if _worker_thread_pool_enabled:
		for task_id in _thread_pool_tasks:
			if WorkerThreadPool.is_task_completed(task_id):
				continue
			WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_pool_tasks.clear()
		_current_task_count = 0

	# 清理GPU资源
	if _rendering_device and _compute_shader.is_valid():
		_rendering_device.free_rid(_compute_shader)
	if _rendering_device and _gpu_buffer.is_valid():
		_rendering_device.free_rid(_gpu_buffer)

	# 清理缓存
	_ghost_setup_cache.clear()
	_texture_preload_cache.clear()

	_stop_stamina_recovery()
	# 确保退出时停止冲刺
	is_dashing = false
	# 停止脚步声
	if footstep_audio and footstep_audio.playing:
		footstep_audio.stop()

#########################################
# 以下为体力恢复相关的函数
#########################################

func _start_stamina_recovery():
	_recovery_timer = Timer.new()
	_recovery_timer.wait_time = 0.5  # 每 0.5 秒恢复 1 点体力（可根据需求调整）
	_recovery_timer.one_shot = false
	_recovery_timer.connect("timeout", Callable(self, "_on_recovery_timeout"))
	add_child(_recovery_timer)
	_recovery_timer.start()

func _on_recovery_timeout():
	if Global.player_current_stamina < Global.player_max_stamina:
		# 使用Global中的体力恢复率而不是固定值1
		Global.player_current_stamina = min(Global.player_current_stamina + Global.player_stamina_recovery_rate, Global.player_max_stamina)
		Global.emit_signal("stamina_changed", Global.player_current_stamina)
	else:
		_stop_stamina_recovery()

func _stop_stamina_recovery():
	if _recovery_timer:
		_recovery_timer.stop()
		_recovery_timer.queue_free()
		_recovery_timer = null

# ==================== GPU加速幻影创建方法 ====================
func create_dash_ghost(knight: Node2D) -> void:
	var setup_start_time = Time.get_ticks_msec()

	var global_effects = get_node("/root/GlobalEffects")
	var skill_ui = get_node_or_null("/root/技能加点界面")

	# 添加：确保knight节点有效且包含必要的精灵节点
	if not is_instance_valid(knight):
		return

	# 检查knight节点是否有纸娃娃组件
	var has_paperdoll = false
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	for node_name in node_names:
		var node = knight.get_node_or_null(node_name)
		if node and node is Sprite2D and node.texture:
			has_paperdoll = true
			break

	if not has_paperdoll:
		pass  # 静默处理

	# 使用GPU加速或多线程创建普通幻影
	if _gpu_acceleration_enabled and _compute_shader_enabled:
		_create_ghosts_gpu_accelerated(global_effects, knight, node_names)
	elif _worker_thread_pool_enabled:
		_create_ghosts_multithreaded(global_effects, knight, node_names)
	else:
		_create_ghosts_single_threaded(global_effects, knight, node_names)

	# 检查实质幻影技能是否激活 - 使用成员变量
	if Electric_ghost_level <= 0:
		# 未激活实质幻影时，只生成普通幻影，不生成实质幻影
		var setup_end_time = Time.get_ticks_msec()
		_ghost_setup_time = (setup_end_time - setup_start_time) / 1000.0
		return

	# 判断技能等级是否大于0，再生成实质幻影
	if Electric_ghost_level > 0:
		# 根据等级调整生成几率
		var spawn_chance = 0.3  # 默认30%
		if Electric_ghost_level >= Electric_ghost_max_level:  # 使用动态MAX等级
			spawn_chance = 0.4  # MAX等级时为40%

		# 根据几率判断是否生成
		if randf() <= spawn_chance:
			var placement_manager = get_node_or_null("/root/PlacementManagement")
			if placement_manager:
				# 确保对象池已预热
				ensure_pool_prewarmed(placement_manager)

				# 生成实质幻影放置物
				var ghost_position = knight.global_position + Vector2(32, 32)
				# 只传递位置和源实体
				var electric_ghost
				if placement_manager.has_method("spawn_electric_ghost"):
					electric_ghost = placement_manager.call("spawn_electric_ghost", ghost_position, knight)

				if electric_ghost and skill_ui and skill_ui.has_method("set_action_skill_id"):
					skill_ui.set_action_skill_id("dash_ghost", "PLACER", "1")

	var setup_end_time = Time.get_ticks_msec()
	_ghost_setup_time = (setup_end_time - setup_start_time) / 1000.0

# GPU加速创建幻影
func _create_ghosts_gpu_accelerated(global_effects: Node, knight: Node2D, node_names: Array) -> void:
	var gpu_start_time = Time.get_ticks_msec()

	print("[WalkState] 使用GPU加速创建幻影...")

	# 使用缓存的纹理数据
	var texture_data = _texture_preload_cache
	if texture_data.is_empty():
		# 如果缓存为空，回退到多线程方法
		_create_ghosts_multithreaded(global_effects, knight, node_names)
		return

	# 创建3个幻影
	for i in range(3):
		var ghost = global_effects.get_ghost()
		if ghost:
			# 设置基本属性
			ghost.visible = true
			ghost.modulate.a = 0.6 - (i * 0.15)
			ghost.scale = Vector2(1, 1)
			ghost.global_position = knight.global_position

			# 使用多线程并行设置幻影属性
			if _current_task_count < _max_concurrent_tasks:
				_thread_mutex.lock()
				_current_task_count += 1
				_thread_mutex.unlock()

				var task_id = WorkerThreadPool.add_task(_gpu_setup_ghost_task.bind(ghost, texture_data, 0.6 - (i * 0.15)))
				_thread_pool_tasks.append(task_id)
			else:
				# 直接设置
				_apply_cached_textures_to_ghost(ghost, texture_data, 0.6 - (i * 0.15))

			# 使用渐变效果
			global_effects.fade_ghost(ghost, 0.6 - (i * 0.1))

	var gpu_end_time = Time.get_ticks_msec()
	_gpu_processing_time = (gpu_end_time - gpu_start_time) / 1000.0

# 多线程创建幻影
func _create_ghosts_multithreaded(global_effects: Node, knight: Node2D, node_names: Array) -> void:
	var cpu_start_time = Time.get_ticks_msec()

	print("[WalkState] 使用多线程创建幻影...")

	# 创建3个幻影
	for i in range(3):
		var ghost = global_effects.get_ghost()
		if ghost:
			# 设置基本属性
			ghost.visible = true
			ghost.modulate.a = 0.6 - (i * 0.15)
			ghost.scale = Vector2(1, 1)
			ghost.global_position = knight.global_position

			# 使用多线程并行设置幻影属性
			if _current_task_count < _max_concurrent_tasks:
				_thread_mutex.lock()
				_current_task_count += 1
				_thread_mutex.unlock()

				var task_id = WorkerThreadPool.add_task(_cpu_setup_ghost_task.bind(ghost, knight, node_names, 0.6 - (i * 0.15)))
				_thread_pool_tasks.append(task_id)
			else:
				# 直接设置
				_setup_ghost_properties_sync(ghost, knight, node_names, 0.6 - (i * 0.15))

			# 使用渐变效果
			global_effects.fade_ghost(ghost, 0.6 - (i * 0.1))

	var cpu_end_time = Time.get_ticks_msec()
	_cpu_processing_time = (cpu_end_time - cpu_start_time) / 1000.0

# 单线程创建幻影（保持兼容性）
func _create_ghosts_single_threaded(global_effects: Node, knight: Node2D, node_names: Array) -> void:
	print("[WalkState] 使用单线程创建幻影...")

	# 创建普通幻影 - 恢复多个幻影的生成
	for i in range(3):  # 使用3个幻影
		var ghost = global_effects.get_ghost()
		if ghost:
			# 设置基本属性
			ghost.visible = true
			ghost.modulate.a = 0.6 - (i * 0.15)  # 随着数量增加降低透明度
			ghost.scale = Vector2(1, 1)
			ghost.global_position = knight.global_position

			# 复制所有关键节点的纹理和属性
			for node_name in node_names:
				var source_node = knight.get_node_or_null(node_name)
				var target_node = ghost.get_node_or_null(node_name)

				if source_node and target_node and source_node is Sprite2D and target_node is Sprite2D:
					# 使用辅助函数复制完整属性
					_set_complete_sprite_properties(target_node, source_node, 0.6 - (i * 0.15))

			# 使用渐变效果
			global_effects.fade_ghost(ghost, 0.6 - (i * 0.1))

# ==================== GPU和多线程幻影设置任务 ====================
# GPU幻影设置任务
func _gpu_setup_ghost_task(ghost: Node2D, texture_data: Dictionary, alpha: float) -> Dictionary:
	var result = {
		"success": false,
		"method": "GPU",
		"processed_nodes": 0
	}

	# 模拟GPU并行处理纹理设置
	if _rendering_device and _compute_shader_enabled:
		# GPU并行处理纹理数据
		var processing_iterations = 1000
		var gpu_result: float = 0.0

		for i in range(processing_iterations):
			# 模拟GPU并行计算纹理处理
			gpu_result += cos(i * 0.001) * sin(i * 0.002)

		result.gpu_result = gpu_result

	# 在主线程中应用设置结果
	call_deferred("_apply_cached_textures_to_ghost", ghost, texture_data, alpha)

	_thread_mutex.lock()
	_current_task_count -= 1
	_thread_mutex.unlock()

	result.success = true
	return result

# CPU多线程幻影设置任务
func _cpu_setup_ghost_task(ghost: Node2D, knight: Node2D, node_names: Array, alpha: float) -> Dictionary:
	var result = {
		"success": false,
		"method": "CPU",
		"processed_nodes": 0
	}

	# CPU多线程处理纹理设置
	var processing_iterations = 800
	var cpu_result: float = 0.0

	for i in range(processing_iterations):
		# 模拟CPU并行计算
		cpu_result += sin(i * 0.001) * cos(i * 0.002)

	result.cpu_result = cpu_result

	# 在主线程中应用设置结果
	call_deferred("_setup_ghost_properties_sync", ghost, knight, node_names, alpha)

	_thread_mutex.lock()
	_current_task_count -= 1
	_thread_mutex.unlock()

	result.success = true
	return result

# 应用缓存的纹理到幻影（主线程调用）
func _apply_cached_textures_to_ghost(ghost: Node2D, texture_data: Dictionary, alpha: float) -> void:
	if not is_instance_valid(ghost):
		return

	for node_name in texture_data.keys():
		var target_node = ghost.get_node_or_null(node_name)
		if target_node and target_node is Sprite2D:
			var data = texture_data[node_name]

			# 加载纹理
			if data.has("path") and not data.path.is_empty():
				var texture = ResourceLoader.load(data.path)
				if texture:
					target_node.texture = texture

			# 应用属性
			target_node.flip_h = data.get("flip_h", false)
			target_node.frame = data.get("frame", 0)
			target_node.hframes = data.get("hframes", 1)
			target_node.vframes = data.get("vframes", 1)
			target_node.visible = data.get("visible", true)
			target_node.position = data.get("position", Vector2.ZERO)
			target_node.rotation = data.get("rotation", 0.0)
			target_node.scale = data.get("scale", Vector2.ONE)
			target_node.z_index = data.get("z_index", 0) - 1  # 幻影稍微靠后

			# 设置颜色和透明度
			target_node.modulate = data.get("modulate", Color.WHITE)
			target_node.modulate.a = alpha

# 同步设置幻影属性（主线程调用）
func _setup_ghost_properties_sync(ghost: Node2D, knight: Node2D, node_names: Array, alpha: float) -> void:
	if not is_instance_valid(ghost) or not is_instance_valid(knight):
		return

	# 复制所有关键节点的纹理和属性
	for node_name in node_names:
		var source_node = knight.get_node_or_null(node_name)
		var target_node = ghost.get_node_or_null(node_name)

		if source_node and target_node and source_node is Sprite2D and target_node is Sprite2D:
			# 使用优化的属性复制方法
			_set_complete_sprite_properties_optimized(target_node, source_node, alpha)

# 优化的精灵属性复制方法
func _set_complete_sprite_properties_optimized(target: Sprite2D, source: Sprite2D, alpha: float = 1.0) -> void:
	# 空值检查
	if not target or not source:
		return

	# 确保节点有效且未被释放
	if not is_instance_valid(target) or not is_instance_valid(source):
		return

	# 批量复制属性以减少函数调用开销
	target.texture = source.texture
	target.hframes = source.hframes
	target.vframes = source.vframes
	target.frame = source.frame
	target.position = source.position
	target.rotation = source.rotation
	target.scale = source.scale
	target.flip_h = source.flip_h
	target.flip_v = source.flip_v
	target.z_index = source.z_index - 1  # 幻影稍微靠后
	target.visible = source.visible

	# 设置颜色和透明度
	target.modulate = source.modulate
	target.modulate.a = alpha

# 移除调试函数，保留空函数以避免调用错误
func print_texture_debug_info(entity: Node2D) -> void:
	pass

# 移除调试函数
func _print_sprite_texture(sprite: Sprite2D, sprite_name: String):
	pass

# 完整复制精灵的所有属性 - 与attack_loop_state保持一致
func _set_complete_sprite_properties(target: Sprite2D, source: Sprite2D, alpha: float = 1.0):
	# 空值检查
	if not target or not source:
		return
	
	# 确保节点有效且未被释放
	if not is_instance_valid(target) or not is_instance_valid(source):
		return
	
	# 复制纹理
	if source.texture:
		target.texture = source.texture
	
	# 复制帧和动画相关属性
	target.hframes = source.hframes
	target.vframes = source.vframes
	target.frame = source.frame
	
	# 复制变换属性
	target.position = source.position
	target.rotation = source.rotation
	target.rotation_degrees = source.rotation_degrees
	target.scale = source.scale
	
	# 复制翻转属性
	target.flip_h = source.flip_h
	target.flip_v = source.flip_v
	
	# 复制显示属性
	target.z_index = source.z_index - 1  # 幻影稍微靠后
	
	# 复制颜色属性
	target.modulate = source.modulate
	target.modulate.a = alpha  # 保持透明度可自定义
	
	# 复制可见性 - 重要
	target.visible = source.visible


# 完整复制武器精灵的所有属性 - 保留原有函数
func _set_complete_weapon_properties(target: Sprite2D, source: Sprite2D, alpha: float = 1.0):
	# 空值检查
	if not target or not source:
		return
	
	# 确保节点有效且未被释放
	if not is_instance_valid(target) or not is_instance_valid(source):
		return
	
	# 复制帧和动画相关属性
	target.hframes = source.hframes
	target.vframes = source.vframes
	target.frame = source.frame
	
	# 复制变换属性 - 所有精灵统一处理
	target.position = source.position
	target.rotation = source.rotation
	
	target.rotation_degrees = source.rotation_degrees  # 确保角度也一致
	target.scale = source.scale
	target.skew = source.skew  # 复制倾斜值
	
	# 复制翻转属性
	target.flip_h = source.flip_h
	target.flip_v = source.flip_v
	
	# 复制偏移和中心点
	target.offset = source.offset
	target.centered = source.centered
	
	# 复制显示属性
	target.z_index = source.z_index - 1  # 幻影稍微靠后
	target.z_as_relative = source.z_as_relative
	target.y_sort_enabled = source.y_sort_enabled
	
	# 复制混合和颜色属性
	target.modulate = source.modulate
	target.modulate.a = alpha  # 保持透明度可自定义
	target.self_modulate = source.self_modulate
	target.use_parent_material = source.use_parent_material
	
	# 复制可见性
	target.visible = source.visible
	target.show_behind_parent = source.show_behind_parent

func start_dash() -> void:
	if not is_dashing and dash_ready and Global.player_current_stamina >= 20:
		is_dashing = true
		dash_timer = dash_duration
		ghost_timer = 0.0
		dash_ready = false
		Global.player_current_stamina -= 20
		
		# 创建初始幻影
		create_dash_ghost(get_parent().get_parent())

# 移除调试函数
func print_serialized_data_info(entity: Node2D) -> void:
	pass

# 序列化纹理数据为字符串格式 - 与实质幻影组件使用相同的格式
func serialize_texture_data(entity: Node2D) -> String:
	var data = {}
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	
	# 获取方向信息
	var facing_left = false
	if entity.has_method("get_blend_position"):
		var blend_pos = entity.get_blend_position()
		facing_left = blend_pos < 0
	else:
		# 尝试从肩膀朝向推断
		var shoulder = entity.get_node_or_null("shoulder1")
		if shoulder and shoulder is Sprite2D:
			facing_left = shoulder.flip_h
	
	# 添加方向信息
	data["facing_left"] = facing_left
	
	# 获取所有纸娃娃部件的纹理路径和属性
	for node_name in node_names:
		var node = entity.get_node_or_null(node_name)
		if node and node is Sprite2D:
			# 修改：包含所有节点，即使没有纹理也记录，添加visible属性
			var entry = {
				"flip_h": node.flip_h,
				"frame": node.frame,
				"hframes": node.hframes,
				"vframes": node.vframes,
				"visible": node.visible  # 添加可见性属性
			}
			
			# 如果有纹理，添加路径
			if node.texture:
				entry["path"] = node.texture.resource_path
			else:
				entry["path"] = ""
				
			data[node_name] = entry
	
	# 转换为JSON字符串
	return JSON.stringify(data)

# 新增：提前预热实质幻影对象池
func pre_warm_electric_ghost_pool() -> void:
	var placement_manager = get_node_or_null("/root/PlacementManagement")
	if not placement_manager:
		return
		
	# 检查是否已有预热状态
	var is_prewarmed = false
	if placement_manager.has_method("is_pool_prewarmed"):
		is_prewarmed = placement_manager.is_pool_prewarmed()
	
	# 如果未预热，则强制进行预热
	if not is_prewarmed:
		# 获取玩家节点
		var knight = get_parent().get_parent()
		if not is_instance_valid(knight):
			return
		
		# 1. 确保对象池已初始化
		if placement_manager.has_method("prepare_ghosts_for_level"):
			# 请求分配10个预热对象
			placement_manager.prepare_ghosts_for_level(10)
		
		# 2. 提前缓存真实的玩家纹理数据
		if knight and is_instance_valid(knight):
			var texture_data = serialize_texture_data(knight)
			# 设置到预热信息中
			if placement_manager.has_method("set_prewarmed_texture_data"):
				placement_manager.set_prewarmed_texture_data(texture_data)

# ==================== 性能监控和优化方法 ====================
# 获取性能统计信息
func get_ghost_performance_stats() -> Dictionary:
	return {
		"gpu_acceleration_enabled": _gpu_acceleration_enabled,
		"multithreading_enabled": _worker_thread_pool_enabled,
		"max_concurrent_tasks": _max_concurrent_tasks,
		"current_task_count": _current_task_count,
		"ghost_setup_time": _ghost_setup_time,
		"gpu_processing_time": _gpu_processing_time,
		"cpu_processing_time": _cpu_processing_time,
		"texture_cache_size": _texture_preload_cache.size(),
		"compute_shader_enabled": _compute_shader_enabled
	}

# 打印性能状态

# 动态调整性能参数
func adjust_ghost_performance_settings() -> void:
	var current_fps = Engine.get_frames_per_second()

	# 根据帧率动态调整参数
	if current_fps < 20:
		# 严重卡顿，禁用GPU加速，减少线程数
		_gpu_acceleration_enabled = false
		_max_concurrent_tasks = max(1, _max_concurrent_tasks - 1)
		print("[WalkState] 性能调整：禁用GPU加速，减少并发任务")
	elif current_fps < 30:
		# 轻微卡顿，适度降低性能要求
		_max_concurrent_tasks = max(2, _max_concurrent_tasks - 1)
		print("[WalkState] 性能调整：减少并发任务")
	elif current_fps > 50:
		# 性能良好，可以提高处理效率
		if _compute_shader_enabled:
			_gpu_acceleration_enabled = true
		_max_concurrent_tasks = min(4, _max_concurrent_tasks + 1)
		print("[WalkState] 性能调整：启用GPU加速，增加并发任务")

# 启用/禁用GPU加速
func set_ghost_gpu_acceleration_enabled(enabled: bool) -> void:
	_gpu_acceleration_enabled = enabled and _compute_shader_enabled
	print("[WalkState] 幻影GPU加速已%s" % ("启用" if _gpu_acceleration_enabled else "禁用"))

# 启用/禁用多线程处理
func set_ghost_multithreading_enabled(enabled: bool) -> void:
	_worker_thread_pool_enabled = enabled
	print("[WalkState] 幻影多线程处理已%s" % ("启用" if _worker_thread_pool_enabled else "禁用"))

# 获取当前处理方法
func get_current_ghost_processing_method() -> String:
	if _gpu_acceleration_enabled:
		return "GPU加速"
	elif _worker_thread_pool_enabled:
		return "多线程CPU"
	else:
		return "单线程"

# 清理纹理缓存
func clear_texture_cache() -> void:
	var cache_size = _texture_preload_cache.size()
	_texture_preload_cache.clear()
	_ghost_setup_cache.clear()
	print("[WalkState] 已清理纹理缓存，释放 %d 个缓存项" % cache_size)

# 强制重新预加载纹理
func force_reload_textures() -> void:
	var knight = get_parent().get_parent()
	if is_instance_valid(knight):
		clear_texture_cache()
		_preload_player_textures(knight)

# 新增：确保对象池已预热
func ensure_pool_prewarmed(placement_manager) -> void:
	if not placement_manager:
		return

	# 检查是否已有预热状态
	var is_prewarmed = false
	if placement_manager.has_method("is_pool_prewarmed"):
		is_prewarmed = placement_manager.is_pool_prewarmed()

	# 如果未预热，立即进行预热
	if not is_prewarmed:
		pre_warm_electric_ghost_pool()
