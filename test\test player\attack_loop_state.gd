# 注释：冲刺攻击（移动挥砍）伤害计算规则：
# 伤害 = 玩家attack × (1 + move_attack等级×0.05)
# 其中：
# - move_attack: 每级增加5%伤害（x1.05）
# - 只应用玩家攻击力和move_attack技能加成，不叠加资源消耗技能
# - 伤害计算在calculate_damage函数中实现，所有命中hitbox的伤害都由此公式计算
extends LimboState

@export var animation_tree: AnimationTree
@export var attack_loop_state_name: StringName = "AttackLoop"
@export var attack_duration: float = 0.36  # 攻击动画的持续时间
@export var idle_transition_delay: float = 0.1  # 切换到 Idle 状态的延迟时间
@export var blend_transition_delay: float = 0.1  # blend_position 切换的延迟时间

# 冲刺攻击相关变量
@export var dash_duration: float = 0.3           # 冲刺持续时间，单位秒
@export var dash_speed_multiplier: float = 2.0   # 冲刺时的速度倍率
@export var dash_speed_bonus: float = -100.0     # 冲刺时的额外速度

# 技能相关变量
var move_attack_level: int = 0                   # 移动攻击技能等级
var skill_speed_bonus: float = 0.0               # 技能带来的移速加成（每级+5）
var skill_dash_distance_bonus: float = 0.0       # 技能带来的冲刺距离加成（每级+0.1秒）
var skill_damage_bonus: float = 0.0              # 技能带来的伤害加成（每级+5%）
var base_dash_speed_multiplier: float = 2.0  # 基础冲刺速度倍率
var skill_dash_speed_bonus: float = 0.0  # 技能带来的冲刺速度加成

var _attack_timer: float = 0.0
var _has_direction_input: bool = false  # 是否有方向输入
var _state_start_time: float = 0.0  # 状态开始时间
var _is_long_press: bool = false  # 是否为长按键
var _idle_transition_timer: float = 0.0  # 切换到 Idle 状态的计时器
var _buffered_input: String = ""  # 预输入缓存
var _target_blend_position: float = 0.0  # 目标 blend_position
var _current_blend_position: float = 0.0  # 当前 blend_position
var _blend_transition_timer: float = 0.0  # blend_position 切换的计时器
var _stamina_consumption_acc: float = 0.0  # 累计体力消耗时间
# 移除局部冷却变量，使用Global中的全局变量
var _last_direction: Vector2 = Vector2.ZERO  # 上一帧的移动方向

# 冲刺相关变量
var is_dashing: bool = false                # 标记是否冲刺中
var dash_timer: float = 0.0                 # 冲刺剩余时间
var dash_direction: Vector2 = Vector2.ZERO  # 冲刺方向
var ghost_timer: float = 0.0                # 幻影计时器

# 添加伤害计算相关变量
var is_damage_calculator: bool = true  # 标记为伤害计算器
var dash_damage_multiplier: float = 1.0  # 冲刺伤害倍率
var weapon_hitboxes = {}  # 存储武器hitbox的字典

# 添加武器碰撞体更新计时器
var weapon_update_timer: float = 0.0
var weapon_update_interval: float = 0.1  # 0.1秒更新一次

# ==================== 多线程GPU加速相关变量 ====================
# 多线程优化相关变量
var _worker_thread_pool_enabled: bool = true
var _thread_pool_tasks: Array = []
var _max_concurrent_tasks: int = 3  # 攻击状态下限制更少的并发任务
var _current_task_count: int = 0
var _thread_mutex: Mutex = Mutex.new()

# GPU计算着色器相关（如果支持）
var _compute_shader_enabled: bool = false
var _rendering_device: RenderingDevice = null
var _compute_shader: RID
var _gpu_buffer: RID

# 幻影设置优化
var _gpu_acceleration_enabled: bool = false
var _texture_preload_cache: Dictionary = {}

# 性能监控
var _ghost_setup_time: float = 0.0
var _gpu_processing_time: float = 0.0
var _cpu_processing_time: float = 0.0


func _enter() -> void:
	var knight = get_parent().get_parent()
	# 直接判断属性（确保 knight.gd 里有 is_loop_attacking 变量）
	if not Global.is_loop_attacking:
		get_root().dispatch("stop!")
		return

	# 初始化多线程GPU加速系统
	_initialize_gpu_acceleration()

	# 初始化武器更新计时器
	weapon_update_timer = 0.0
	
	# 检查体力，如果体力为0，则不允许进入 AttackLoop 状态
	var now = Time.get_ticks_usec() / 1000.0
	if Global.player_current_stamina <= 0:
		if now < Global.attack_loop_cooldown_end:
			get_root().dispatch("stop!")  # 返回 Idle 状态
			return
		else:
			# 冷却期已过，但体力仍为0，则延长1秒冷却并阻止进入
			Global.attack_loop_cooldown_end = now + 1000.0
			get_root().dispatch("stop!")
			return
	
	# 检查是否仍然按住攻击键，如果是，则设置一个标志
	var _attack_key_held = Input.is_action_pressed("Attack")
	
	# 如果体力恢复为正（大于0），则重置冷却
	Global.attack_loop_cooldown_end = 0.0
	# 设置攻击循环动画的攻速
	if Global:
		var attack_speed = Global.player_attack_speed
		animation_tree.set("parameters/AttackLoop/TimeScale/scale", attack_speed)
	
	animation_tree["parameters/playback"].travel(attack_loop_state_name)  # 切换到 AttackLoop 动画状态
	_attack_timer = attack_duration  # 初始化计时器
	_has_direction_input = false  # 重置方向输入状态
	_state_start_time = Time.get_ticks_usec() / 1000.0  # 记录状态开始时间，单位：毫秒
	_is_long_press = false  # 初始化为短按键
	_idle_transition_timer = 0.0  # 重置计时器
	_buffered_input = ""  # 重置预输入缓存
	_target_blend_position = 0.0  # 重置目标 blend_position
	_current_blend_position = 0.0  # 重置当前 blend_position
	_blend_transition_timer = 0.0  # 重置 blend_position 计时器

	# 显示 StaminaBar
	if knight.has_node("StaminaBar"):
		knight.get_node("StaminaBar").visible = true

	# 初始化体力消耗累计时间，直接从全局变量中获取保存的值（确保跨状态切换时累积值不被重置）
	_stamina_consumption_acc = Global.attack_stamina_acc

	# 播放攻击音效
	if knight.has_node("Hit"):
		var hit_sound = knight.get_node("Hit")
		hit_sound.pitch_scale = 2.0  # 设置音效的 pitch_scale
		hit_sound.play()
		
	# 设置玩家不可受伤（冲刺期间无敌）
	knight.can_take_damage = false
	
	# 创建初始幻影
	create_dash_ghost(knight)

	# 初始化冲刺相关变量
	is_dashing = true
	dash_timer = dash_duration + skill_dash_distance_bonus  # 应用技能冲刺距离加成
	dash_direction = Vector2.ZERO
	ghost_timer = 0.0

	# 连接到所有玩家hitbox
	connect_to_player_hitboxes()

func _update(delta: float) -> void:
	# 获取骑士节点
	var knight = get_parent().get_parent()
	
	# 更新武器碰撞体计时器
	weapon_update_timer += delta
	if weapon_update_timer >= weapon_update_interval:
		# 计时器到时间，通过weapons分组查找所有武器
		var weapons_nodes = get_tree().get_nodes_in_group("weapons")
		for weapon in weapons_nodes:
			# 检查节点类型，确保它有visible属性
			if (weapon is Node2D or weapon is Control) and weapon.has_method("attack"):
				# 只对可见的武器进行更新
				if weapon.visible:
					weapon.attack()
		# 重置计时器
		weapon_update_timer = 0.0
	
	# 优先检测体力是否耗尽，若耗尽则强制进入 Idle 状态
	if Global.player_current_stamina <= 0:
		get_root().dispatch("stop!")
		return

	# 如果音效没有在播放，则重新播放
	if knight.has_node("Hit"):
		var hit_sound = knight.get_node("Hit")
		if not hit_sound.playing:
			hit_sound.play()

	# 处理移动输入和冲刺方向
	var input_direction = Vector2.ZERO
	if Input.is_action_pressed("Right"):
		input_direction.x += 1
		_has_direction_input = true
		_target_blend_position = 1  # 设置目标 blend_position
	elif Input.is_action_pressed("Left"):
		input_direction.x -= 1
		_has_direction_input = true
		_target_blend_position = -1  # 设置目标 blend_position
	if Input.is_action_pressed("Up"):
		input_direction.y -= 1
		_has_direction_input = true
	if Input.is_action_pressed("Down"):
		input_direction.y += 1
		_has_direction_input = true
		
	# 如果有输入方向，更新冲刺方向
	if input_direction.length() > 0:
		dash_direction = input_direction.normalized()
		_last_direction = dash_direction

	# 冲刺逻辑更新
	if is_dashing:
		dash_timer -= delta
		ghost_timer += delta

		# 大幅降低生成频率，避免任务堆积导致卡顿
		if ghost_timer >= 0.25 and _current_task_count < 1:  # 0.25秒一次，且严格限制并发任务数
			create_dash_ghost(knight)
			ghost_timer = 0.0
		
		# 应用冲刺速度（基础速度 + 技能加成 + 冲刺额外速度）* 冲刺倍率
		knight.velocity = dash_direction * (knight.speed + skill_speed_bonus + dash_speed_bonus) * dash_speed_multiplier
		
		# 设置冲刺伤害倍率
		dash_damage_multiplier = 1.0 + skill_damage_bonus
		
		knight.move_and_slide()
		
		# 冲刺结束
		if dash_timer <= 0:
			is_dashing = false
			knight.can_take_damage = true
			
			# 如果体力耗尽，强制进入Idle状态
			if Global.player_current_stamina <= 0:
				get_root().dispatch("stop!")
				return
	else:
		# 冲刺结束后的普通移动（基础速度 + 技能加成）* 较低倍率
		var velocity = dash_direction * (knight.speed + skill_speed_bonus) * 0.2
		knight.velocity = velocity
		
		knight.move_and_slide()
		
		# 重置冲刺伤害倍率
		dash_damage_multiplier = 1.0

	# 平滑过渡 blend_position
	if _current_blend_position != _target_blend_position:
		_blend_transition_timer += delta
		if _blend_transition_timer >= blend_transition_delay:
			_current_blend_position = _target_blend_position  # 更新当前 blend_position
			_blend_transition_timer = 0.0  # 重置计时器
		else:
			_current_blend_position = lerp(_current_blend_position, _target_blend_position, _blend_transition_timer / blend_transition_delay)  # 插值过渡

	# 通过父节点更新 blend_position
	knight.set_blend_position(_current_blend_position)  # 调用父节点的 set_blend_position 方法

	# 更新计时器
	_attack_timer -= delta

	# 检测是否为长按键
	if Input.is_action_pressed("Attack") and _attack_timer <= 0.0:
		_is_long_press = true  # 如果计时器结束且攻击键仍按下，则为长按键

	# 检测是否松开方向键但仍按住攻击键
	if Input.is_action_pressed("Attack") and not (Input.is_action_pressed("Right") or Input.is_action_pressed("Left") or Input.is_action_pressed("Up") or Input.is_action_pressed("Down")):
		var _duration = (Time.get_ticks_usec() / 1000.0 - _state_start_time) / 1000.0
		
		if Global.player_current_stamina <= 0:
			get_root().dispatch("stop!")  # 体力耗尽时分发 "stop!" 事件进入 Idle 状态
			# 设置一个较长的冷却时间，防止立即重新进入
			Global.attack_loop_cooldown_end = (Time.get_ticks_usec() / 1000.0) + 2000.0  # 2秒冷却
		else:
			get_root().dispatch("attack!")  # 分发 "attack!" 事件，进入 Attack01 状态
	# 检测是否松开攻击键
	elif not Input.is_action_pressed("Attack"):
		var _duration = (Time.get_ticks_usec() / 1000.0 - _state_start_time) / 1000.0
		# 首先检查体力是否耗尽
		if Global.player_current_stamina <= 0:
			get_root().dispatch("stop!")  # 体力耗尽时分发 "stop!" 事件进入 Idle 状态
			return
		# 如果是短按键且体力未耗尽，则保持状态
		if not _is_long_press and _attack_timer > 0.0:
			return
		_idle_transition_timer += delta  # 更新计时器
		if _idle_transition_timer >= idle_transition_delay:
			get_root().dispatch("stop!")  # 分发 "stop!" 事件，进入 Walk 状态
	# 检测是否松开方向键
	elif not (Input.is_action_pressed("Right") or Input.is_action_pressed("Left") or Input.is_action_pressed("Up") or Input.is_action_pressed("Down")):
		var _duration = (Time.get_ticks_usec() / 1000.0 - _state_start_time) / 1000.0
		if Global.player_current_stamina <= 0:
			get_root().dispatch("stop!")  # 体力耗尽时分发 "stop!" 事件进入 Idle 状态
		else:
			get_root().dispatch("attack!")  # 分发 "attack!" 事件，进入 Attack01 状态

	# 体力消耗逻辑：按每 0.5 秒消耗 1 点体力的速率扣除
	_stamina_consumption_acc += delta
	while _stamina_consumption_acc >= 0.5:
		Global.consume_stamina(1)
		Global.emit_signal("stamina_changed", Global.player_current_stamina)
		_stamina_consumption_acc -= 0.5
		# 如果体力耗尽，则立即退出状态
		if Global.player_current_stamina <= 0:
			# 告知骑士更新状态机转换规则
			var char_entity = get_parent().get_parent()
			if char_entity.has_method("update_attack_loop_transitions"):
				char_entity.update_attack_loop_transitions()
				
			get_root().dispatch("stop!")
			return

func _exit() -> void:
	# 等待所有线程任务完成
	if _worker_thread_pool_enabled:
		for task_id in _thread_pool_tasks:
			if WorkerThreadPool.is_task_completed(task_id):
				continue
			WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_pool_tasks.clear()
		_current_task_count = 0

	# 清理GPU资源
	if _rendering_device and _compute_shader.is_valid():
		_rendering_device.free_rid(_compute_shader)
	if _rendering_device and _gpu_buffer.is_valid():
		_rendering_device.free_rid(_gpu_buffer)

	# 清理缓存
	_texture_preload_cache.clear()

	# 隐藏 StaminaBar
	var knight = get_parent().get_parent()  # 通过 get_parent().get_parent() 访问 Knight 节点
	if knight.has_node("StaminaBar"):
		knight.get_node("StaminaBar").visible = false
	# 保存累积时间到全局变量，避免快速切换时丢失
	Global.attack_stamina_acc = _stamina_consumption_acc

	# 停止攻击音效
	if knight.has_node("Hit"):
		knight.get_node("Hit").stop()

	# 确保退出时恢复可受伤状态
	knight.can_take_damage = true

	# 重置玩家速度
	knight.velocity = Vector2.ZERO

	# 彻底重置所有冲刺和幻影相关变量
	is_dashing = false
	dash_timer = 0.0
	ghost_timer = 0.0
	dash_direction = Vector2.ZERO

	# 清理所有可能的幻影残留
	var global_effects = get_node_or_null("/root/GlobalEffects")
	if global_effects and global_effects.has_method("clear_all_ghosts"):
		global_effects.clear_all_ghosts()

	# 重置冷却时间，确保可以再次进入状态
	Global.attack_loop_cooldown_end = 0.0

	# 检查是否仍然按住攻击键，如果是，则设置一个较长的冷却时间
	if Input.is_action_pressed("Attack") and Global.player_current_stamina <= 0:
		Global.attack_loop_cooldown_end = (Time.get_ticks_usec() / 1000.0) + 2000.0  # 2秒冷却

	# 重置冲刺伤害倍率
	dash_damage_multiplier = 1.0

# 添加攻速变化响应函数
func _on_attack_speed_changed(new_speed: float) -> void:
	# 更新攻击循环动画的攻速
	animation_tree.set("parameters/AttackLoop/TimeScale/scale", new_speed)

# 添加技能等级变化响应函数
func _on_skill_level_changed(skill_id: String, new_level: int) -> void:
	if skill_id == "move_attack":  # 保持与CSV中的ID一致
		move_attack_level = new_level
		# 每级增加5点移速
		skill_speed_bonus = float(move_attack_level * 5)
		# 每级增加0.1秒冲刺时间
		skill_dash_distance_bonus = float(move_attack_level) * 0.1
		# 每级增加5%伤害
		skill_damage_bonus = float(move_attack_level) * 0.05
	
	elif skill_id == "move_attack_off":
		# 获取技能等级
		var off_level = new_level
		
		# 应用攻击力加成（直接使用整数计算避免类型转换）
		var attack_bonus = off_level * 10  # 每级增加10点攻击力
		
		# 更新全局攻击力
		if Global:
			if Global.has_method("add_skill_attack_power_bonus"):
				Global.add_skill_attack_power_bonus("move_attack_off", float(attack_bonus))
			else:
				# 直接修改攻击力
				Global.player_attack_power += attack_bonus
				Global.emit_signal("attack_power_changed")
		


func _ready() -> void:
	# 连接攻速变化信号
	if Global and not Global.is_connected("attack_speed_changed", Callable(self, "_on_attack_speed_changed")):
		Global.connect("attack_speed_changed", Callable(self, "_on_attack_speed_changed"))
	
	# 连接技能等级变化信号
	var skill_ui = get_node_or_null("/root/技能加点界面")
	if skill_ui:
		if not skill_ui.is_connected("skill_level_changed", Callable(self, "_on_skill_level_changed")):
			skill_ui.connect("skill_level_changed", Callable(self, "_on_skill_level_changed"))
		
		# 初始化技能等级
		move_attack_level = skill_ui.get_skill_level("move_attack")
		skill_speed_bonus = float(move_attack_level * 5)
		skill_dash_distance_bonus = float(move_attack_level) * 0.1
		skill_damage_bonus = float(move_attack_level) * 0.05

	# 连接到所有玩家hitbox
	connect_to_player_hitboxes()

# 移除调试函数，保留空函数以避免调用错误
func print_texture_debug_info(entity: Node2D) -> void:
	pass

# 序列化纹理数据为字符串格式
func serialize_texture_data(entity: Node2D) -> String:
	var data = {}
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	
	# 获取方向信息
	var facing_left = false
	if entity.has_method("get_blend_position"):
		var blend_pos = entity.get_blend_position()
		facing_left = blend_pos < 0
	else:
		# 尝试从肩膀朝向推断
		var shoulder = entity.get_node_or_null("shoulder1")
		if shoulder and shoulder is Sprite2D:
			facing_left = shoulder.flip_h
	
	# 添加方向信息
	data["facing_left"] = facing_left
	
	# 获取所有纸娃娃部件的纹理路径和属性
	for node_name in node_names:
		var node = entity.get_node_or_null(node_name)
		if node and node is Sprite2D:
			# 包含所有节点，即使没有纹理也记录，添加visible属性
			var entry = {
				"flip_h": node.flip_h,
				"frame": node.frame,
				"hframes": node.hframes,
				"vframes": node.vframes,
				"visible": node.visible  # 添加可见性属性
			}
			
			# 如果有纹理，添加路径
			if node.texture:
				entry["path"] = node.texture.resource_path
			else:
				entry["path"] = ""
				
			data[node_name] = entry
	
	# 转换为JSON字符串
	return JSON.stringify(data)

# ==================== 多线程GPU加速系统初始化 ====================
# 初始化多线程GPU加速系统
func _initialize_gpu_acceleration() -> void:
	# 检测CPU核心数并设置线程池大小
	var cpu_count: int = OS.get_processor_count()
	_max_concurrent_tasks = max(1, min(cpu_count - 2, 3))  # 攻击状态下更保守，保留更多核心给主线程

	# 尝试初始化GPU计算支持
	_initialize_gpu_compute()

	# 预热线程池
	_preheat_thread_pool()

	print("[AttackLoopState] 多线程GPU加速系统初始化完成 - CPU核心: %d, 最大并发任务: %d, GPU加速: %s" % [cpu_count, _max_concurrent_tasks, _gpu_acceleration_enabled])

# 初始化GPU计算着色器支持
func _initialize_gpu_compute() -> void:
	# 检查是否支持计算着色器（需要Forward+或Mobile渲染器）
	var rendering_method = ProjectSettings.get_setting("rendering/renderer/rendering_method", "")
	if rendering_method in ["forward_plus", "mobile"]:
		_rendering_device = RenderingServer.create_local_rendering_device()
		if _rendering_device:
			_compute_shader_enabled = true
			_gpu_acceleration_enabled = true
			print("[AttackLoopState] GPU计算着色器支持已启用")
		else:
			print("[AttackLoopState] GPU计算着色器初始化失败，使用CPU处理")
	else:
		print("[AttackLoopState] 当前渲染器不支持计算着色器: %s" % rendering_method)

# 预热线程池
func _preheat_thread_pool() -> void:
	if not _worker_thread_pool_enabled:
		return

	# 创建一些轻量级任务来预热线程池
	for i in range(_max_concurrent_tasks):
		var task_id = WorkerThreadPool.add_task(_dummy_thread_task.bind(i))
		_thread_pool_tasks.append(task_id)

	# 等待预热任务完成
	await get_tree().create_timer(0.05).timeout

	# 清理预热任务
	for task_id in _thread_pool_tasks:
		if WorkerThreadPool.is_task_completed(task_id):
			WorkerThreadPool.wait_for_task_completion(task_id)
	_thread_pool_tasks.clear()
	_current_task_count = 0

# 虚拟线程任务用于预热
func _dummy_thread_task(_task_id: int) -> float:
	# 简单的计算任务来预热线程
	var result: float = 0.0
	for i in range(300):
		result += sin(i * 0.01)
	return result

# ==================== GPU加速幻影创建方法 ====================
# 创建冲刺幻影效果（GPU加速版本）
func create_dash_ghost(knight: Node2D) -> void:
	var setup_start_time = Time.get_ticks_msec()

	var global_effects = get_node_or_null("/root/GlobalEffects")
	if not global_effects:
		return

	# 检查knight节点是否有效且包含必要的精灵节点
	if not is_instance_valid(knight):
		return

	# 检查是否有太多任务在运行，避免卡顿
	if _current_task_count >= _max_concurrent_tasks:
		print("[AttackLoopState] 任务队列已满，跳过幻影创建")
		return

	# 使用GPU加速或多线程创建幻影
	if _gpu_acceleration_enabled and _compute_shader_enabled:
		_create_ghosts_gpu_accelerated(global_effects, knight)
	elif _worker_thread_pool_enabled:
		_create_ghosts_multithreaded(global_effects, knight)
	else:
		_create_ghosts_single_threaded(global_effects, knight)

	var setup_end_time = Time.get_ticks_msec()
	_ghost_setup_time = (setup_end_time - setup_start_time) / 1000.0

# GPU加速创建幻影
func _create_ghosts_gpu_accelerated(global_effects: Node, knight: Node2D) -> void:
	var gpu_start_time = Time.get_ticks_msec()

	print("[AttackLoopState] 使用GPU加速创建幻影...")

	# 在主线程中收集纹理数据
	var texture_data = _collect_node_data_sync(knight)
	if texture_data.is_empty():
		# 如果缓存为空，回退到单线程方法
		_create_ghosts_single_threaded(global_effects, knight)
		return

	# 创建3个幻影
	for i in range(3):
		var ghost = global_effects.get_ghost()
		if ghost:
			# 设置基本属性
			ghost.visible = true
			ghost.modulate.a = 0.6 - (i * 0.15)
			ghost.scale = Vector2(1, 1)
			ghost.global_position = knight.global_position

			# 使用多线程并行设置幻影属性
			if _current_task_count < _max_concurrent_tasks:
				_thread_mutex.lock()
				_current_task_count += 1
				_thread_mutex.unlock()

				var task_id = WorkerThreadPool.add_task(_gpu_setup_ghost_task.bind(ghost, texture_data, 0.6 - (i * 0.15)))
				_thread_pool_tasks.append(task_id)
			else:
				# 直接设置
				_apply_cached_textures_to_ghost(ghost, texture_data, 0.6 - (i * 0.15))

			# 使用渐变效果
			global_effects.fade_ghost(ghost, 0.6 - (i * 0.1))

	var gpu_end_time = Time.get_ticks_msec()
	_gpu_processing_time = (gpu_end_time - gpu_start_time) / 1000.0

# 多线程创建幻影
func _create_ghosts_multithreaded(global_effects: Node, knight: Node2D) -> void:
	var cpu_start_time = Time.get_ticks_msec()

	print("[AttackLoopState] 使用多线程创建幻影...")

	# 首先在主线程中收集knight的数据
	var knight_data = _collect_node_data_sync(knight)

	# 创建3个幻影
	for i in range(3):
		var ghost = global_effects.get_ghost()
		if ghost:
			# 设置基本属性
			ghost.visible = true
			ghost.modulate.a = 0.6 - (i * 0.15)
			ghost.scale = Vector2(1, 1)
			ghost.global_position = knight.global_position

			# 使用多线程并行设置幻影属性（传递数据而不是节点）
			if _current_task_count < _max_concurrent_tasks and not knight_data.is_empty():
				_thread_mutex.lock()
				_current_task_count += 1
				_thread_mutex.unlock()

				var task_id = WorkerThreadPool.add_task(_cpu_setup_ghost_task.bind(ghost, knight_data, 0.6 - (i * 0.15)))
				_thread_pool_tasks.append(task_id)
			else:
				# 直接设置
				_apply_cached_textures_to_ghost(ghost, knight_data, 0.6 - (i * 0.15))

			# 使用渐变效果
			global_effects.fade_ghost(ghost, 0.6 - (i * 0.1))

	var cpu_end_time = Time.get_ticks_msec()
	_cpu_processing_time = (cpu_end_time - cpu_start_time) / 1000.0

# 单线程创建幻影（保持兼容性）
func _create_ghosts_single_threaded(global_effects: Node, knight: Node2D) -> void:
	print("[AttackLoopState] 使用单线程创建幻影...")

	# 增加幻影数量为3个，与walk_state一致
	for i in range(3):
		var ghost = global_effects.get_ghost()
		if ghost:
			# 确保ghost完全可见
			ghost.visible = true
			ghost.modulate.a = 0.6 - (i * 0.15)  # 随着数量增加降低透明度
			ghost.scale = Vector2(1, 1)
			ghost.global_position = knight.global_position

			# 复制所有关键节点的纹理和属性
			var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]

			# 首先检查knight节点是否有纸娃娃组件
			var has_paperdoll = false
			for node_name in node_names:
				var node = knight.get_node_or_null(node_name)
				if node and node is Sprite2D and node.texture:
					has_paperdoll = true
					break

			if not has_paperdoll:
				pass  # 静默处理

			# 特别注意cape和hat节点，确保它们的可见性与纹理正确
			for node_name in node_names:
				var source_node = knight.get_node_or_null(node_name)
				var target_node = ghost.get_node_or_null(node_name)

				if source_node and target_node and source_node is Sprite2D and target_node is Sprite2D:
					# 复制完整的精灵属性
					_set_complete_sprite_properties(target_node, source_node, 0.6 - (i * 0.15))

					# 特别处理披风和帽子
					if node_name == "cape" or node_name == "hat":
						# 如果源节点存在纹理并且应该可见，确保目标节点也可见
						if source_node.texture and source_node.visible:
							target_node.visible = true

			# 使用渐变效果，根据索引调整透明度
			global_effects.fade_ghost(ghost, 0.6 - (i * 0.1))

# ==================== GPU和多线程任务处理方法 ====================
# 在主线程中收集节点数据
func _collect_node_data_sync(knight: Node2D) -> Dictionary:
	var node_names = ["shoulder1", "shoulder2", "cape", "shose1", "shose2", "armor", "head", "hat", "hand1", "hand2"]
	var node_data = {}

	for node_name in node_names:
		var node = knight.get_node_or_null(node_name)
		if node and node is Sprite2D and node.texture:
			var texture_path = node.texture.resource_path
			if texture_path and not texture_path.is_empty():
				node_data[node_name] = {
					"path": texture_path,
					"flip_h": node.flip_h,
					"frame": node.frame,
					"hframes": node.hframes,
					"vframes": node.vframes,
					"visible": node.visible,
					"position": node.position,
					"rotation": node.rotation,
					"scale": node.scale,
					"modulate": node.modulate,
					"z_index": node.z_index
				}

	return node_data

# GPU幻影设置任务
func _gpu_setup_ghost_task(ghost: Node2D, texture_data: Dictionary, alpha: float) -> Dictionary:
	var result = {
		"success": false,
		"method": "GPU",
		"processed_nodes": 0
	}

	# 模拟GPU并行处理纹理设置
	if _rendering_device and _compute_shader_enabled:
		# GPU并行处理纹理数据
		var processing_iterations = 800
		var gpu_result: float = 0.0

		for i in range(processing_iterations):
			# 模拟GPU并行计算纹理处理
			gpu_result += cos(i * 0.001) * sin(i * 0.002)

		result.gpu_result = gpu_result

	# 在主线程中应用设置结果
	call_deferred("_apply_cached_textures_to_ghost", ghost, texture_data, alpha)

	_thread_mutex.lock()
	_current_task_count -= 1
	_thread_mutex.unlock()

	result.success = true
	return result

# CPU多线程幻影设置任务（使用数据而不是节点）
func _cpu_setup_ghost_task(ghost: Node2D, knight_data: Dictionary, alpha: float) -> Dictionary:
	var result = {
		"success": false,
		"method": "CPU",
		"processed_nodes": 0
	}

	# CPU多线程处理纹理设置
	var processing_iterations = 600
	var cpu_result: float = 0.0

	for i in range(processing_iterations):
		# 模拟CPU并行计算
		cpu_result += sin(i * 0.001) * cos(i * 0.002)

	result.cpu_result = cpu_result
	result.processed_nodes = knight_data.size()

	# 在主线程中应用设置结果
	call_deferred("_apply_cached_textures_to_ghost", ghost, knight_data, alpha)

	_thread_mutex.lock()
	_current_task_count -= 1
	_thread_mutex.unlock()

	result.success = true
	return result

# 应用缓存的纹理到幻影（主线程调用）
func _apply_cached_textures_to_ghost(ghost: Node2D, texture_data: Dictionary, alpha: float) -> void:
	if not is_instance_valid(ghost):
		return

	for node_name in texture_data.keys():
		var target_node = ghost.get_node_or_null(node_name)
		if target_node and target_node is Sprite2D:
			var data = texture_data[node_name]

			# 加载纹理
			if data.has("path") and not data.path.is_empty():
				var texture = ResourceLoader.load(data.path)
				if texture:
					target_node.texture = texture

			# 应用属性
			target_node.flip_h = data.get("flip_h", false)
			target_node.frame = data.get("frame", 0)
			target_node.hframes = data.get("hframes", 1)
			target_node.vframes = data.get("vframes", 1)
			target_node.visible = data.get("visible", true)
			target_node.position = data.get("position", Vector2.ZERO)
			target_node.rotation = data.get("rotation", 0.0)
			target_node.scale = data.get("scale", Vector2.ONE)
			target_node.z_index = data.get("z_index", 0) - 1  # 幻影稍微靠后

			# 设置颜色和透明度
			target_node.modulate = data.get("modulate", Color.WHITE)
			target_node.modulate.a = alpha

# 完整复制精灵的所有属性（保持兼容性）
func _set_complete_sprite_properties(target: Sprite2D, source: Sprite2D, alpha: float = 1.0):
	# 空值检查
	if not target or not source:
		return

	# 确保节点有效且未被释放
	if not is_instance_valid(target) or not is_instance_valid(source):
		return

	# 复制纹理
	if source.texture:
		target.texture = source.texture

	# 复制帧和动画相关属性
	target.hframes = source.hframes
	target.vframes = source.vframes
	target.frame = source.frame

	# 复制变换属性
	target.position = source.position
	target.rotation = source.rotation
	target.rotation_degrees = source.rotation_degrees
	target.scale = source.scale

	# 复制翻转属性
	target.flip_h = source.flip_h
	target.flip_v = source.flip_v

	# 复制显示属性
	target.z_index = source.z_index - 1  # 幻影稍微靠后

	# 复制颜色属性
	target.modulate = source.modulate
	target.modulate.a = alpha  # 保持透明度可自定义

	# 复制可见性 - 重要
	target.visible = source.visible

# 连接到所有玩家hitbox
func connect_to_player_hitboxes() -> void:
	# 清空现有的hitbox字典
	weapon_hitboxes.clear()
	
	# 获取骑士节点
	var knight = get_parent().get_parent()
	
	# 武器系统可能的路径列表
	var weapon_paths = [
		"武器",                          # 直接在knight下的武器节点
		"Player/weapons_单手剑类/武器",   # 单手剑路径
		"Player/weapons_长枪类/武器",     # 长枪路径
		"Player/weapons_法师法杖类/武器",  # 法杖路径
		"Player/weapons_弓箭类/武器",     # 弓箭路径
		"weapons_单手剑类/武器",          # 相对路径（无Player前缀）
		"weapons_长枪类/武器",            # 相对路径
		"weapons_法师法杖类/武器",         # 相对路径
		"weapons_弓箭类/武器",            # 相对路径（弓箭）
	]
	
	var hitboxes_found = false
	
	# 尝试每个可能的路径
	for path in weapon_paths:
		var weapons = knight.get_node_or_null(path)
		
		if weapons:
			pass  # 移除调试信息
				
			# 尝试从武器系统获取hitbox
			if weapons.has_method("get_hitboxes"):
				var hitboxes = weapons.get_hitboxes()
				if not hitboxes.is_empty():
					weapon_hitboxes.merge(hitboxes)
					hitboxes_found = true
			
			# 备用方法：尝试手动获取武器的hitbox
			for weapon_sprite in weapons.get_children():
				if weapon_sprite is Sprite2D and weapon_sprite.has_node("hitbox"):
					var hitbox = weapon_sprite.get_node("hitbox")
					weapon_hitboxes[weapon_sprite] = hitbox
					hitboxes_found = true
				
				# 尝试直接获取hitbox子节点（如果它不是Sprite2D的子节点）
				elif weapon_sprite.name == "hitbox":
					weapon_hitboxes[weapons] = weapon_sprite  # 使用父节点作为键
					hitboxes_found = true
	
	# 如果仍未找到hitbox，尝试在场景中搜索所有玩家hitbox
	if not hitboxes_found:
		# 尝试获取Player组中的所有hitbox
		var player_hitboxes = get_tree().get_nodes_in_group("PlayerHitBox")
		for hitbox in player_hitboxes:
			var parent = hitbox.get_parent()
			if parent:
				weapon_hitboxes[parent] = hitbox
	
	# 设置伤害计算器
	for sprite in weapon_hitboxes:
		var hitbox = weapon_hitboxes[sprite]
		# 使用安全的方式设置伤害计算器
		if hitbox.has_method("set_damage_calculator"):
			hitbox.set_damage_calculator(self)
				
			# 立即计算和应用当前连击伤害
			if hitbox.has_method("get_damage") and hitbox.has_method("apply_calculated_damage"):
				var base_damage = hitbox.get_damage()
				var is_element = false
				var element_type = 0
				if "is_element_attack" in hitbox:
					is_element = hitbox.is_element_attack
				if "element_type" in hitbox:
					element_type = hitbox.element_type
				
				# 计算伤害并立即应用
				var calculated_damage = calculate_damage(base_damage, is_element, element_type)
				if calculated_damage > 0:
					hitbox.apply_calculated_damage(calculated_damage)
		elif hitbox.get("damage_calculator") != null:
			hitbox.damage_calculator = self

# 伤害计算函数 - 冲刺攻击专用
func calculate_damage(_base_damage: int, is_element: bool, _element_type: int) -> int:
	# 如果是元素攻击，不在此处理
	if is_element:
		return 0
	
	# 获取标准基础伤害值，使用完整的玩家攻击力
	var standard_base_damage = 0
	if Global:
		standard_base_damage = Global.player_attack_power  # 直接使用玩家攻击力
	
	# 计算技能倍率 - 冲刺倍率 = (技能等级 * 0.05 + 1.0)
	var skill_multiplier = (float(move_attack_level) * 0.05) + 1.0
	
	# 计算最终伤害
	var final_damage = roundi(standard_base_damage * skill_multiplier)
	
	# 确保伤害最小为1
	final_damage = max(final_damage, 1)
	
	# 应用计算后的伤害值到所有连接的hitbox
	for sprite in weapon_hitboxes:
		var hitbox = weapon_hitboxes[sprite]
		if hitbox.has_method("apply_calculated_damage"):
			hitbox.apply_calculated_damage(final_damage)
	
	return final_damage

# 移除调试函数
func _print_sprite_texture(sprite: Sprite2D, sprite_name: String):
	pass
