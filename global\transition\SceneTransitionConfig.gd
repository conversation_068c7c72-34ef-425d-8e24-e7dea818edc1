extends Resource

# SceneTransition GPU加速配置文件
# 用于保存和管理场景切换的性能设置

class_name SceneTransitionConfig

# GPU加速设置
@export var gpu_acceleration_enabled: bool = true
@export var force_gpu_for_large_scenes: bool = true
@export var gpu_fallback_enabled: bool = true

# 多线程设置
@export var multithreading_enabled: bool = true
@export var max_worker_threads: int = 4
@export var thread_pool_preheating: bool = true

# 性能优化设置
@export var auto_performance_adjustment: bool = true
@export var performance_monitoring: bool = false
@export var scene_caching_enabled: bool = true
@export var max_cached_scenes: int = 10

# 加载时间设置
@export var min_loading_time: float = 0.5
@export var loading_timeout: float = 10.0
@export var fade_in_time: float = 0.5
@export var fade_out_time: float = 1.2

# 场景复杂度阈值设置
@export var simple_scene_threshold_mb: float = 1.0
@export var complex_scene_threshold_mb: float = 5.0

# 性能阈值设置
@export var low_fps_threshold: float = 20.0
@export var good_fps_threshold: float = 50.0
@export var critical_fps_threshold: float = 15.0

# 预加载设置
@export var auto_preload_enabled: bool = false
@export var preload_common_scenes: Array[String] = []
@export var preload_on_startup: bool = false

# 调试设置
@export var debug_logging: bool = false
@export var performance_logging: bool = false
@export var detailed_timing: bool = false

# 默认配置
static func create_default_config() -> SceneTransitionConfig:
	var config = SceneTransitionConfig.new()
	
	# 根据系统性能设置默认值
	var cpu_count = OS.get_processor_count()
	config.max_worker_threads = max(2, min(cpu_count - 1, 6))
	
	# 检查渲染器支持
	var rendering_method = ProjectSettings.get_setting("rendering/renderer/rendering_method", "")
	config.gpu_acceleration_enabled = rendering_method in ["forward_plus", "mobile"]
	
	return config

# 应用配置到SceneTransition
func apply_to_scene_transition() -> void:
	if not SceneTransition:
		print("[配置] 错误：找不到SceneTransition节点")
		return
	
	print("[配置] 应用SceneTransition配置...")
	
	# 应用GPU设置
	SceneTransition.set_gpu_acceleration_enabled(gpu_acceleration_enabled)
	SceneTransition.set_multithreading_enabled(multithreading_enabled)
	SceneTransition.set_performance_monitoring_enabled(performance_monitoring)
	
	# 应用时间设置
	SceneTransition.min_load_time = min_loading_time
	SceneTransition.loading_timeout = loading_timeout
	SceneTransition.fade_in_time = fade_in_time
	SceneTransition.fade_out_time = fade_out_time
	
	# 应用线程设置
	SceneTransition._max_concurrent_tasks = max_worker_threads
	
	print("[配置] 配置应用完成")
	print("  - GPU加速: %s" % gpu_acceleration_enabled)
	print("  - 多线程: %s" % multithreading_enabled)
	print("  - 最大线程数: %d" % max_worker_threads)
	print("  - 性能监控: %s" % performance_monitoring)

# 从SceneTransition读取当前配置
func load_from_scene_transition() -> void:
	if not SceneTransition:
		print("[配置] 错误：找不到SceneTransition节点")
		return
	
	gpu_acceleration_enabled = SceneTransition.is_gpu_acceleration_enabled()
	multithreading_enabled = SceneTransition.is_multithreading_enabled()
	
	var stats = SceneTransition.get_performance_stats()
	max_worker_threads = stats.max_concurrent_tasks
	
	print("[配置] 已从SceneTransition读取当前配置")

# 保存配置到文件
func save_config(file_path: String = "user://scene_transition_config.tres") -> bool:
	var error = ResourceSaver.save(self, file_path)
	if error == OK:
		print("[配置] 配置已保存到: %s" % file_path)
		return true
	else:
		print("[配置] 保存配置失败: %s" % error)
		return false

# 从文件加载配置
static func load_config(file_path: String = "user://scene_transition_config.tres") -> SceneTransitionConfig:
	if ResourceLoader.exists(file_path):
		var config = ResourceLoader.load(file_path)
		if config is SceneTransitionConfig:
			print("[配置] 配置已从文件加载: %s" % file_path)
			return config
		else:
			print("[配置] 配置文件格式错误，使用默认配置")
	else:
		print("[配置] 配置文件不存在，创建默认配置")
	
	return create_default_config()

# 重置为默认配置
func reset_to_default() -> void:
	var default_config = create_default_config()
	
	gpu_acceleration_enabled = default_config.gpu_acceleration_enabled
	multithreading_enabled = default_config.multithreading_enabled
	max_worker_threads = default_config.max_worker_threads
	auto_performance_adjustment = default_config.auto_performance_adjustment
	performance_monitoring = default_config.performance_monitoring
	scene_caching_enabled = default_config.scene_caching_enabled
	max_cached_scenes = default_config.max_cached_scenes
	min_loading_time = default_config.min_loading_time
	loading_timeout = default_config.loading_timeout
	fade_in_time = default_config.fade_in_time
	fade_out_time = default_config.fade_out_time
	
	print("[配置] 已重置为默认配置")

# 验证配置有效性
func validate_config() -> bool:
	var is_valid = true
	var errors = []
	
	if max_worker_threads < 1 or max_worker_threads > 16:
		errors.append("最大线程数必须在1-16之间")
		is_valid = false
	
	if min_loading_time < 0.1 or min_loading_time > 5.0:
		errors.append("最小加载时间必须在0.1-5.0秒之间")
		is_valid = false
	
	if loading_timeout < 1.0 or loading_timeout > 60.0:
		errors.append("加载超时时间必须在1-60秒之间")
		is_valid = false
	
	if fade_in_time < 0.1 or fade_in_time > 3.0:
		errors.append("淡入时间必须在0.1-3.0秒之间")
		is_valid = false
	
	if fade_out_time < 0.1 or fade_out_time > 3.0:
		errors.append("淡出时间必须在0.1-3.0秒之间")
		is_valid = false
	
	if max_cached_scenes < 0 or max_cached_scenes > 50:
		errors.append("最大缓存场景数必须在0-50之间")
		is_valid = false
	
	if not is_valid:
		print("[配置] 配置验证失败:")
		for error in errors:
			print("  - %s" % error)
	
	return is_valid

# 获取配置摘要
func get_config_summary() -> String:
	var summary = []
	summary.append("SceneTransition配置摘要:")
	summary.append("  GPU加速: %s" % ("启用" if gpu_acceleration_enabled else "禁用"))
	summary.append("  多线程: %s" % ("启用" if multithreading_enabled else "禁用"))
	summary.append("  最大线程数: %d" % max_worker_threads)
	summary.append("  场景缓存: %s" % ("启用" if scene_caching_enabled else "禁用"))
	summary.append("  性能监控: %s" % ("启用" if performance_monitoring else "禁用"))
	summary.append("  自动性能调节: %s" % ("启用" if auto_performance_adjustment else "禁用"))
	summary.append("  最小加载时间: %.1f秒" % min_loading_time)
	summary.append("  加载超时: %.1f秒" % loading_timeout)
	
	return "\n".join(summary)

# 优化配置（根据当前系统性能）
func optimize_for_current_system() -> void:
	print("[配置] 正在为当前系统优化配置...")
	
	var cpu_count = OS.get_processor_count()
	var current_fps = Engine.get_frames_per_second()
	var rendering_method = ProjectSettings.get_setting("rendering/renderer/rendering_method", "")
	
	# 根据CPU核心数调整线程数
	max_worker_threads = max(2, min(cpu_count - 1, 6))
	
	# 根据渲染器调整GPU设置
	gpu_acceleration_enabled = rendering_method in ["forward_plus", "mobile"]
	
	# 根据当前FPS调整性能设置
	if current_fps < low_fps_threshold:
		# 低性能设备优化
		scene_caching_enabled = false
		auto_performance_adjustment = true
		max_cached_scenes = 3
		min_loading_time = 0.8
		print("[配置] 检测到低性能设备，应用保守设置")
	elif current_fps > good_fps_threshold:
		# 高性能设备优化
		scene_caching_enabled = true
		max_cached_scenes = 15
		min_loading_time = 0.3
		print("[配置] 检测到高性能设备，应用激进设置")
	else:
		# 中等性能设备
		scene_caching_enabled = true
		max_cached_scenes = 8
		min_loading_time = 0.5
		print("[配置] 检测到中等性能设备，应用平衡设置")
	
	print("[配置] 系统优化完成:")
	print("  CPU核心数: %d" % cpu_count)
	print("  当前FPS: %.1f" % current_fps)
	print("  渲染器: %s" % rendering_method)
	print("  线程数: %d" % max_worker_threads)
	print("  GPU加速: %s" % gpu_acceleration_enabled)
