#@brief 冒险岛式区域怪物生成与管理系统（优化版 - 防重复生成）
#@description
#这是一个高度优化的冒险岛式区域怪物生成管理系统，专为游戏世界中的动态怪物管理而设计。
#
#系统设计理念：
#- 冒险岛式补充：怪物死亡后不立即补充，而是定时批量补充
#- 防重复生成：统一使用单一补充机制，避免同一生成点重复生成怪物
#- 性能优化：限制同时活跃区域数量，减少预加载卡顿
#- 智能管理：基于区域优先级和距离限制进行智能管理
#
#主要功能模块：
#1. 冒险岛式补充机制
#   - 怪物死亡后隐藏一段时间，然后批量补充
#   - 定时全局补充，避免玩家感觉怪物没有被杀死
#   - 支持区域优先级管理，优先补充当前区域
#
#2. 性能优化系统
#   - 限制最大同时活跃区域数（默认3个）
#   - 分批处理生成队列，避免单帧卡顿
#   - 定期清理无效数据和非活跃区域
#
#3. 智能区域管理
#   - 基于玩家位置动态调整区域优先级
#   - 只处理相关区域的生成点状态变化
#   - 延迟区域补充，模拟冒险岛的隐藏效果
#
#4. 内存与性能优化
#   - 异步怪物生成，减少主线程卡顿
#   - 批处理生成和清理机制
#   - 使用实例ID绑定，避免内存泄漏
#   - 缓存生成点信息，避免重复计算
#
#核心优化特性：
#- 冒险岛式定时批量补充（5秒间隔）
#- 怪物死亡后2秒隐藏时间
#- 最大3个同时活跃区域
#- 每批次最多生成3个怪物
#- 30秒定期清理无效数据
#
#性能参数：
#- 全局补充间隔：5秒
#- 怪物隐藏时间：2秒
#- 最大活跃区域：3个
#- 每批次生成数：3个
#- 清理间隔：30秒
#
#@note 专为解决怪物补充过快和预加载卡顿问题而优化
#@version 2.0 - 冒险岛式优化版
#@date 2025-07-23
#<AUTHOR>
#"""
extends TileMapLayer

signal monster_spawn_completed(area_key)  # 标记怪物生成完成

# 生成点管理字典（使用弱引用减少内存占用）
var spawn_points: Dictionary = {}

# 新增：生成点ID到怪物ID的映射
var spawn_point_to_monster_id: Dictionary = {}
var monster_id_to_spawn_point: Dictionary = {}

# 性能优化配置
const MAX_MONSTERS_PER_POINT = 1  # 每个生成点最大怪物数
const SPAWN_COOLDOWN = 3.0  # 生成点冷却时间（秒）
const SPAWN_QUEUE_INTERVAL = 0.8  # 增加生成间隔，减少掉帧
const MAX_SPAWN_POINTS_PER_FRAME = 2  # 降低每帧处理的生成点数量，防止掉帧
const MAX_CONSECUTIVE_SPAWN_ATTEMPTS = 3  # 连续生成尝试次数限制

# 新增：全局生成数量控制（提高生成效率）
const MAX_CONCURRENT_SPAWNS = 8  # 提高最大同时生成数量
const MAX_SPAWNS_PER_SECOND = 6  # 提高每秒生成数量

# 缓存区域生成点信息，避免重复计算
var _area_spawn_points_cache: Dictionary = {}

# 异步生成队列
var _async_spawn_queue: Array = []
var _is_processing_spawn_queue: bool = false

# 多线程优化相关变量
var _worker_thread_pool_enabled: bool = true
var _thread_pool_tasks: Array = []
var _max_concurrent_tasks: int = 4
var _current_task_count: int = 0
var _thread_mutex: Mutex = Mutex.new()
# var _thread_semaphore: Semaphore = Semaphore.new()  # 暂时未使用，保留备用

# GPU计算着色器相关（如果支持）
var _compute_shader_enabled: bool = false
var _rendering_device: RenderingDevice = null
var _compute_shader: RID
var _spawn_buffer: RID

# 冒险岛式集体补充机制相关变量
var global_replenish_timer := 0.0 # 全局补充计时器
var global_replenish_interval := 5.0 # 全局补充间隔（秒）- 冒险岛式定时补充
var monster_hide_duration := 2.0 # 怪物死亡后隐藏时间（秒）
var batch_respawn_enabled := true # 是否启用批量补充

# 性能优化：分区域管理
var active_area_limit := 3 # 最大同时活跃区域数
var preload_distance_limit := 2 # 预加载距离限制（瓦片单位）

# 统一使用冒险岛式多线程异步队列，移除旧的Timer生成系统
var spawn_per_batch := 4  # 每批次生成的怪物数量（提高生成效率）

# 新增：全局生成控制变量
var current_spawning_count := 0  # 当前正在生成的怪物数量
var last_spawn_time := 0.0  # 上次生成时间
var spawn_rate_limiter := 0.0  # 生成速率限制器

# 区域优先级管理
var area_priority: Dictionary = {} # 区域优先级 {area_key: priority_value}
var last_cleanup_time := 0.0 # 上次清理时间
var cleanup_interval := 30.0 # 清理间隔（秒）

# 新增：预加载区域管理
var preload_areas: Array = []  # 替换 active_areas
var spawn_point_areas: Dictionary = {}  # 生成点ID -> 归属区域列表的映射
var area_spawn_points: Dictionary = {}  # 区域 -> 生成点ID列表的映射

# 新增：地图预加载管理变量
var current_preloaded_map := ""  # 记录当前预加载的地图名称
var preloaded_areas: Array = []  # 记录当前预加载地图中的所有区域

# 记录当前活跃区域
var last_entered_area := ""

# 新增：全局已占用坐标记录
var global_occupied_positions: Dictionary = {}

# 移除全局锁机制，简化逻辑

# 初始化
func _ready():
	# 连接全局怪物死亡信号
	if GlobalEnemies.has_signal("enemy_death_immediate"):
		GlobalEnemies.enemy_death_immediate.connect(_on_monster_died)

	# 初始化多线程和GPU计算支持
	_initialize_performance_optimizations()

# 初始化性能优化系统
func _initialize_performance_optimizations():
	# 检测CPU核心数并设置线程池大小
	var cpu_count: int = OS.get_processor_count()
	_max_concurrent_tasks = max(2, min(cpu_count - 1, 6))  # 保留一个核心给主线程

	# 尝试初始化GPU计算支持
	_initialize_gpu_compute()

	# 预热线程池
	_preheat_thread_pool()

# 初始化GPU计算着色器支持
func _initialize_gpu_compute():
	# 检查是否支持计算着色器（需要Forward+或Mobile渲染器）
	var rendering_method = ProjectSettings.get_setting("rendering/renderer/rendering_method", "")
	if rendering_method in ["forward_plus", "mobile"]:
		_rendering_device = RenderingServer.create_local_rendering_device()
		if _rendering_device:
			_compute_shader_enabled = true
			# 这里可以加载计算着色器文件进行怪物位置计算等

# 预热线程池
func _preheat_thread_pool():
	# 创建一些轻量级任务来预热线程池
	for i in range(_max_concurrent_tasks):
		var task_id = WorkerThreadPool.add_task(_dummy_thread_task.bind(i))
		_thread_pool_tasks.append(task_id)

	# 等待预热任务完成
	await get_tree().create_timer(0.1).timeout

# 虚拟线程任务用于预热
func _dummy_thread_task(_task_id: int):
	# 简单的计算任务来预热线程
	var result: float = 0.0
	for i in range(1000):
		result += sin(i * 0.01)
	return result

# 生成唯一的生成点ID（使用坐标而不是区域名称）
func _generate_spawn_point_id(_area_key: String, tile_pos: Vector2i) -> String:
	return "%d_%d" % [tile_pos.x, tile_pos.y]

# 检查坐标是否已被占用
func _is_position_occupied(tile_pos: Vector2i) -> bool:
	var pos_key = "%d_%d" % [tile_pos.x, tile_pos.y]
	return global_occupied_positions.has(pos_key)

# 标记坐标为已占用
func _mark_position_occupied(tile_pos: Vector2i, area_key: String):
	var pos_key = "%d_%d" % [tile_pos.x, tile_pos.y]
	global_occupied_positions[pos_key] = area_key

# 清理坐标占用标记
func _clear_position_occupied(tile_pos: Vector2i):
	var pos_key = "%d_%d" % [tile_pos.x, tile_pos.y]
	if global_occupied_positions.has(pos_key):
		global_occupied_positions.erase(pos_key)

# 注册怪物到生成点
func register_monster_to_spawn_point(monster: Node, spawn_point_id: String):
	if not is_instance_valid(monster):
		return
	
	var monster_id = monster.get_instance_id()
	spawn_point_to_monster_id[spawn_point_id] = monster_id
	monster_id_to_spawn_point[monster_id] = spawn_point_id
	
	# 设置怪物的spawn_key用于识别
	if monster.has_method("set_spawn_key"):
		monster.set_spawn_key(spawn_point_id)
	elif "spawn_key" in monster:
		monster.spawn_key = spawn_point_id

# 检查怪物是否属于某个生成点
func is_monster_belong_to_spawn_point(monster: Node, spawn_point_id: String) -> bool:
	if not is_instance_valid(monster):
		return false
	
	var monster_id = monster.get_instance_id()
	return spawn_point_to_monster_id.get(spawn_point_id, -1) == monster_id

# 根据怪物ID获取生成点ID
func get_spawn_point_id_by_monster(monster: Node) -> String:
	if not is_instance_valid(monster):
		return ""
	
	var monster_id = monster.get_instance_id()
	return monster_id_to_spawn_point.get(monster_id, "")

# 清理怪物绑定
func unregister_monster_from_spawn_point(monster: Node):
	if not is_instance_valid(monster):
		return
	
	var monster_id = monster.get_instance_id()
	var spawn_point_id = monster_id_to_spawn_point.get(monster_id, "")
	
	if spawn_point_id != "":
		spawn_point_to_monster_id.erase(spawn_point_id)
		monster_id_to_spawn_point.erase(monster_id)

# 冒险岛式怪物死亡处理
func _on_monster_died(monster: Node, spawn_key: String = ""):
	# 如果没有提供spawn_key，尝试从怪物获取
	if spawn_key == "":
		spawn_key = get_spawn_point_id_by_monster(monster)

	if spawn_key == "":
		return

	# 清理绑定
	unregister_monster_from_spawn_point(monster)

	# 解析spawn_key获取坐标信息（新的格式是 "x_y"）
	var parts = spawn_key.split("_")
	if parts.size() >= 2:
		var x = int(parts[0])
		var y = int(parts[1])
		var tile_pos = Vector2i(x, y)

		# 清理坐标占用标记
		_clear_position_occupied(tile_pos)

		# 找到对应的生成点并标记为待补充（冒险岛式）
		# 修复：更新所有包含这个坐标的区域中的生成点状态
		for area_key in spawn_points.keys():
			if spawn_points[area_key].has(tile_pos):
				var spawn_point = spawn_points[area_key][tile_pos]
				# 检查这个生成点是否确实属于死亡的怪物
				var point_spawn_id = spawn_point.get("spawn_point_id", "")
				if point_spawn_id == spawn_key:
					spawn_point.current_monster = null
					spawn_point.is_spawning = false

				# 不再降低区域优先级，避免影响其他生成点的补充

# 冒险岛式区域切换管理（彻底重写版：精确清理逻辑）
func on_area_changed(area_key: String):
	# 防止重复处理同一区域切换事件
	if area_key == last_entered_area:
		return

	var previous_area = last_entered_area
	last_entered_area = area_key

	print("[生成管理器] 区域切换: %s -> %s" % [previous_area, area_key])

	# 重置生成计数器和清理队列（修复：彻底重置）
	current_spawning_count = 0
	spawn_rate_limiter = 0.0
	_async_spawn_queue.clear()
	_is_processing_spawn_queue = false  # 重置队列处理状态

	# 1. 智能清理：只清理真正不在新区域预加载范围内的怪物（保护存活怪物）
	_smart_cleanup_monsters_for_area_change(area_key)

	# 2. 确保当前区域初始化（不强制重新扫描，避免清空怪物）
	if not spawn_points.has(area_key) or spawn_points[area_key].is_empty():
		print("[生成管理器] 初始化缺失区域: %s" % area_key)
		initialize_spawn_points(area_key)

	# 3. 确保所有包含当前区域的生成点区域都初始化
	_ensure_related_areas_initialized(area_key)

	# 4. 更新所有生成点的激活状态
	_update_all_spawn_points_activation(area_key)

	# 5. 立即触发一次生成检查
	call_deferred("_trigger_batch_respawn")

	# 6. 发送完成信号
	emit_signal("monster_spawn_completed", area_key)

	print("[生成管理器] 区域切换完成，当前活跃区域: %s" % area_key)

# 确保包含当前区域的所有生成点区域都初始化
func _ensure_related_areas_initialized(area_key: String):
	var used_cells = get_used_cells()
	var areas_to_initialize = []

	for tile_pos in used_cells:
		var tile_data = get_cell_tile_data(tile_pos)
		if not tile_data:
			continue

		# 收集这个瓦片所属的所有区域
		var tile_areas = []
		for i in 4:
			var area_mark = tile_data.get_custom_data("预加载区域_%d" % (i + 1))
			if area_mark != null and area_mark != "":
				tile_areas.append(area_mark)

		# 如果当前区域在这个瓦片的区域列表中，就需要初始化这个瓦片所在的区域
		if area_key in tile_areas:
			# 获取怪物类型，确认这是一个有效的生成点瓦片
			var has_monster_type = false
			for i in 3:
				var monster_type = tile_data.get_custom_data("怪物类型_%d" % (i + 1))
				if monster_type != null and monster_type > 0:
					has_monster_type = true
					break

			if has_monster_type:
				# 确定这个生成点应该属于哪个区域（使用第一个相关区域作为主区域）
				for related_area in tile_areas:
					if related_area not in areas_to_initialize:
						areas_to_initialize.append(related_area)

	# 初始化所有需要的区域
	for related_area in areas_to_initialize:
		if not spawn_points.has(related_area):

			_initialize_spawn_points_data_only(related_area)

# 更新所有生成点的激活状态（优化版：智能激活，避免不必要的停用）
func _update_all_spawn_points_activation(area_key: String):
	print("[生成管理器] 更新生成点激活状态，当前区域: %s" % area_key)

	var activated_count = 0
	var kept_active_count = 0

	# 遍历所有已初始化的区域
	for spawn_area in spawn_points.keys():
		for tile_pos in spawn_points[spawn_area].keys():
			var spawn_point = spawn_points[spawn_area][tile_pos]
			var related_areas = spawn_point.get("related_areas", [])

			# 智能激活逻辑：如果当前区域在这个生成点的预加载区域中，就激活它
			var should_be_active = area_key in related_areas
			var was_active = spawn_point.get("is_active", false)

			if should_be_active:
				spawn_point["is_active"] = true
				if not was_active:
					activated_count += 1
				else:
					kept_active_count += 1
			# 注意：不再主动停用生成点，让智能清理函数处理

	print("[生成管理器] 激活状态更新完成: 新激活=%d, 保持激活=%d" % [activated_count, kept_active_count])

# 处理预加载区域变化
func _handle_preload_area_change():
	# 清理所有非预加载区域的怪物
	for area_key in spawn_points.keys():
		if area_key not in preload_areas:
			_cleanup_area_monsters(area_key)
	
	# 确保当前预加载区域有怪物
	for area_key in preload_areas:
		if spawn_points.has(area_key):
			_ensure_area_monsters(area_key)

# 清理区域怪物
func _cleanup_area_monsters(area_key: String):
	# 如果是当前预加载地图的区域，不清理
	if preloaded_areas.has(area_key) && current_preloaded_map != "":
		print("[生成管理器] 跳过清理预加载区域: ", area_key)
		return
	
	if not spawn_points.has(area_key):
		return
	
	for tile_pos in spawn_points[area_key].keys():
		var spawn_point = spawn_points[area_key][tile_pos]
		var spawn_point_id = spawn_point.get("spawn_point_id", "")
		
		if spawn_point_id != "":
			var monster_id = spawn_point_to_monster_id.get(spawn_point_id, -1)
			if monster_id != -1:
				var monster = instance_from_id(monster_id)
				if monster and is_instance_valid(monster):
					# 安全移除怪物
					if monster.is_inside_tree():
						monster.queue_free()
					
					# 清理绑定
					spawn_point_to_monster_id.erase(spawn_point_id)
					monster_id_to_spawn_point.erase(monster_id)
					
					# 清理坐标占用标记
					_clear_position_occupied(tile_pos)
		
		# 重置生成点状态
		spawn_point.current_monster = null
		spawn_point.is_spawning = false
		spawn_point.spawn_cooldown = SPAWN_COOLDOWN
		spawn_point.spawn_attempts = 0

# 确保区域有怪物（修改：移除重复生成，统一使用冒险岛式补充）
func _ensure_area_monsters(area_key: String):
	# 如果区域不存在，先初始化
	if not spawn_points.has(area_key):
		_initialize_spawn_points_data_only(area_key)

	# 不再立即生成怪物，统一由冒险岛式补充系统处理
	# 这样避免了与_trigger_batch_respawn的冲突
	print("[生成管理器] 区域 %s 已准备，等待冒险岛式补充系统处理" % area_key)

# 只初始化生成点数据结构，不开始生成队列
func _initialize_spawn_points_data_only(area_key: String):
	# 如果该区域已经初始化过且有生成点，直接返回
	if spawn_points.has(area_key) and not spawn_points[area_key].is_empty():
		return
	
	# 检查缓存
	if _area_spawn_points_cache.has(area_key):
		spawn_points[area_key] = _area_spawn_points_cache[area_key].duplicate()
		# 还需要恢复区域映射关系
		for tile_pos in spawn_points[area_key].keys():
			var spawn_point = spawn_points[area_key][tile_pos]
			var spawn_point_id = spawn_point.get("spawn_point_id", "")
			if spawn_point_id != "" and spawn_point_areas.has(spawn_point_id):
				var areas = spawn_point_areas[spawn_point_id]
				for related_area in areas:
					if not area_spawn_points.has(related_area):
						area_spawn_points[related_area] = []
					if spawn_point_id not in area_spawn_points[related_area]:
						area_spawn_points[related_area].append(spawn_point_id)
		return
	
	# 清理旧的区域生成点
	spawn_points[area_key] = {}
	
	# 查找并初始化生成点（使用更高效的遍历方式）
	var used_cells = get_used_cells()

	# 预分配内存，提高性能
	used_cells.resize(used_cells.size())
	
	for tile_pos in used_cells:
		var tile_data = get_cell_tile_data(tile_pos)
		if not tile_data:
			continue
		
		# 收集这个瓦片所属的所有区域（检查所有活跃区域字段）
		var tile_areas = []
		
		# 检查 custom_data_0 到 custom_data_3（对应活跃区域_1到活跃区域_4）
		for i in 4:
			var area_mark = tile_data.get_custom_data("预加载区域_%d" % (i + 1))
			if area_mark != null and area_mark != "":
				tile_areas.append(area_mark)
		
		# 如果这个瓦片有区域标记且有怪物类型，就处理它
		if not tile_areas.is_empty():
			# 检查这个坐标是否已被占用
			if _is_position_occupied(tile_pos):
				continue

			# 获取怪物类型（检查 custom_data_4, custom_data_5, custom_data_6）
			var monster_types = []
			for i in 3:
				var monster_type = tile_data.get_custom_data("怪物类型_%d" % (i + 1))
				if monster_type != null and monster_type > 0:
					monster_types.append(monster_type)

			if not monster_types.is_empty():
				var spawn_point_id = _generate_spawn_point_id(area_key, tile_pos)

				# 为所有相关区域初始化这个生成点
				for related_area in tile_areas:
					# 确保相关区域的spawn_points字典存在
					if not spawn_points.has(related_area):
						spawn_points[related_area] = {}

					# 只有当前处理的区域或者生成点不存在时才创建
					if related_area == area_key or not spawn_points[related_area].has(tile_pos):
						spawn_points[related_area][tile_pos] = {
							"monster_types": monster_types,
							"current_monster": null,
							"is_spawning": false,
							"max_monsters": MAX_MONSTERS_PER_POINT,
							"spawn_cooldown": 0.0,
							"spawn_attempts": 0,
							"spawn_point_id": spawn_point_id,
							"related_areas": tile_areas.duplicate(),
							"is_active": true  # 初始化时就激活，简化逻辑
						}

				# 记录生成点的区域归属关系（全局唯一）
				spawn_point_areas[spawn_point_id] = tile_areas.duplicate()

				# 记录区域到生成点的映射
				for related_area in tile_areas:
					if not area_spawn_points.has(related_area):
						area_spawn_points[related_area] = []
					if spawn_point_id not in area_spawn_points[related_area]:
						area_spawn_points[related_area].append(spawn_point_id)

				# 标记这个坐标为已占用
				_mark_position_occupied(tile_pos, area_key)
	
	# 缓存生成点信息
	_area_spawn_points_cache[area_key] = spawn_points[area_key].duplicate()

# GPU计算优化：批量计算生成点位置和状态
func _gpu_batch_calculate_spawn_positions(spawn_tasks: Array) -> Array:
	if not _compute_shader_enabled or spawn_tasks.size() < 10:
		# 如果GPU计算不可用或任务数量太少，回退到CPU计算
		return _cpu_batch_calculate_spawn_positions(spawn_tasks)

	# 这里可以实现GPU计算着色器来并行计算大量生成点的位置转换
	# 由于计算着色器的复杂性，这里提供一个框架
	print("[生成管理器] 使用GPU批量计算 %d 个生成点位置" % spawn_tasks.size())

	# 准备GPU缓冲区数据
	var input_data: PackedFloat32Array = PackedFloat32Array()
	for task in spawn_tasks:
		var tile_pos: Vector2i = task.tile_pos
		input_data.append(float(tile_pos.x))
		input_data.append(float(tile_pos.y))

	# 这里应该调用计算着色器进行并行计算
	# 目前回退到CPU计算
	return _cpu_batch_calculate_spawn_positions(spawn_tasks)

# CPU批量计算生成点位置（作为GPU计算的回退方案）
func _cpu_batch_calculate_spawn_positions(spawn_tasks: Array) -> Array:
	var results: Array = []

	for task in spawn_tasks:
		var tile_pos: Vector2i = task.tile_pos
		var world_pos = map_to_local(tile_pos)
		var global_pos = to_global(world_pos)

		results.append({
			"area_key": task.area_key,
			"tile_pos": tile_pos,
			"global_pos": global_pos,
			"monster_type_id": task.monster_type_id
		})

	return results

# 智能批量处理：根据系统负载动态调整批次大小（平衡策略）
func _get_optimal_batch_size() -> int:
	var base_batch_size: int = spawn_per_batch

	# 根据当前帧率动态调整（更平衡的策略）
	var current_fps = Engine.get_frames_per_second()
	if current_fps < 20:
		# 严重掉帧，限制为1
		base_batch_size = 1
	elif current_fps < 30:
		# 轻微掉帧，限制为2
		base_batch_size = min(2, base_batch_size)
	elif current_fps > 45:
		# 性能良好时允许增加
		base_batch_size = min(5, base_batch_size + 1)

	# 根据当前生成数量调整（更宽松）
	if current_spawning_count >= MAX_CONCURRENT_SPAWNS * 0.8:
		base_batch_size = max(1, base_batch_size - 1)

	# 根据当前线程池负载调整（更宽松）
	if _current_task_count >= _max_concurrent_tasks * 0.8:
		base_batch_size = max(1, base_batch_size - 1)

	return max(1, base_batch_size)

# 清理方法（增强版，包含多线程资源清理）
func clear_spawn_points():
	# 等待所有线程任务完成
	if _worker_thread_pool_enabled:
		for task_id in _thread_pool_tasks:
			if WorkerThreadPool.is_task_completed(task_id):
				continue
			WorkerThreadPool.wait_for_task_completion(task_id)
		_thread_pool_tasks.clear()
		_current_task_count = 0

	spawn_points.clear()
	_area_spawn_points_cache.clear()
	_async_spawn_queue.clear()
	_is_processing_spawn_queue = false

	# 清理区域活跃度管理相关数据
	spawn_point_areas.clear()
	area_spawn_points.clear()
	global_occupied_positions.clear()
	# 移除全局锁清理

	# 移除不再需要的预加载地图相关变量
	current_preloaded_map = ""
	preloaded_areas.clear()

	# 旧的Timer系统已移除，统一使用冒险岛式补充

	# 清理GPU资源
	if _compute_shader_enabled and _rendering_device:
		if _spawn_buffer.is_valid():
			_rendering_device.free_rid(_spawn_buffer)
		if _compute_shader.is_valid():
			_rendering_device.free_rid(_compute_shader)

# 冒险岛式补充系统主循环
func _process(delta):
	# 更新生成速率限制器
	spawn_rate_limiter += delta

	# 全局补充计时器
	global_replenish_timer += delta

	# 定期清理无效数据
	last_cleanup_time += delta
	if last_cleanup_time >= cleanup_interval:
		_cleanup_invalid_data()
		last_cleanup_time = 0.0

	# 冒险岛式定时批量补充（增加速率限制）
	if global_replenish_timer >= global_replenish_interval and batch_respawn_enabled:
		# 检查生成速率限制
		if spawn_rate_limiter >= (1.0 / MAX_SPAWNS_PER_SECOND):
			_trigger_batch_respawn()
			global_replenish_timer = 0.0
			spawn_rate_limiter = 0.0

	# 定期调整性能参数（每5秒检查一次）
	if int(global_replenish_timer) % 5 == 0 and global_replenish_timer > 0:
		adjust_performance_settings()

# 清理无效数据（优化版：只清理真正无效的数据，不影响存活怪物）
func _cleanup_invalid_data():
	# 清理无效的怪物绑定
	var invalid_spawn_points = []
	for spawn_point_id in spawn_point_to_monster_id.keys():
		var monster_id = spawn_point_to_monster_id[spawn_point_id]
		var monster = instance_from_id(monster_id)
		if not monster or not is_instance_valid(monster):
			invalid_spawn_points.append(spawn_point_id)

	for spawn_point_id in invalid_spawn_points:
		var monster_id = spawn_point_to_monster_id[spawn_point_id]
		spawn_point_to_monster_id.erase(spawn_point_id)
		monster_id_to_spawn_point.erase(monster_id)

	# 移除自动清理非活跃区域，避免误删存活怪物
	# _cleanup_inactive_areas()  # 只在手动切换区域时调用

# 冒险岛式批量补充触发器（修复版：正常生成逻辑）
func _trigger_batch_respawn():
	if not batch_respawn_enabled:
		return

	print("[生成管理器] 开始冒险岛式批量补充检查，当前区域: %s" % last_entered_area)

	# 正常生成逻辑：不依赖密度，直接检查需要补充的生成点
	var max_spawns_this_round = 8  # 每次最多补充8只怪物
	var total_spawned = 0

	# 遍历所有区域的所有生成点（原子操作：检查并立即标记）
	for area_key in spawn_points.keys():
		for tile_pos in spawn_points[area_key].keys():
			var spawn_point = spawn_points[area_key][tile_pos]

			# 原子操作：检查并立即标记，防止竞态条件
			if _should_respawn_at_point(area_key, tile_pos) and not spawn_point.get("is_spawning", false):
				# 立即标记为正在生成（防止重复检查）
				spawn_point["is_spawning"] = true

				var valid_monster_types = spawn_point.monster_types.filter(func(type):
					var md = GlobalEnemies.get_monster_data(type)
					return md != null and not md.is_empty()
				)

				if not valid_monster_types.is_empty():
					var monster_type_id = int(valid_monster_types[randi() % valid_monster_types.size()])

					# 添加到异步生成队列
					_async_spawn_queue.append({
						"area_key": area_key,
						"tile_pos": tile_pos,
						"monster_type_id": monster_type_id
					})

					total_spawned += 1
					print("[生成管理器] 标记生成点并添加到队列: 区域=%s, 位置=(%d,%d)" % [area_key, tile_pos.x, tile_pos.y])

					# 达到本轮补充上限就停止
					if total_spawned >= max_spawns_this_round:
						break
				else:
					# 没有有效怪物类型，取消标记
					spawn_point["is_spawning"] = false

		if total_spawned >= max_spawns_this_round:
			break

	print("[生成管理器] 批量补充完成: 添加到队列=%d" % total_spawned)

	# 启动异步生成队列处理
	if not _async_spawn_queue.is_empty() and not _is_processing_spawn_queue:
		print("[生成管理器] 启动异步生成队列处理，队列长度: %d" % _async_spawn_queue.size())
		_process_async_spawn_queue()
	elif _is_processing_spawn_queue:
		print("[生成管理器] 异步队列正在处理中，跳过")
	else:
		print("[生成管理器] 异步队列为空，无需处理")

# 计算当前活跃区域的怪物密度（简化版）
func _calculate_current_monster_density() -> float:
	var total_active_spawn_points = 0
	var spawn_points_with_monsters = 0

	# 只统计当前玩家区域预加载范围内的生成点
	for area_key in spawn_points.keys():
		for tile_pos in spawn_points[area_key].keys():
			# 检查是否在当前玩家区域的预加载范围内
			if _is_spawn_point_in_area_preload_range(tile_pos, last_entered_area):
				total_active_spawn_points += 1

				var spawn_point = spawn_points[area_key][tile_pos]
				var spawn_point_id = spawn_point.get("spawn_point_id", "")

				if spawn_point_id != "":
					var monster_id = spawn_point_to_monster_id.get(spawn_point_id, -1)
					if monster_id != -1:
						var monster = instance_from_id(monster_id)
						if monster and is_instance_valid(monster):
							spawn_points_with_monsters += 1

	if total_active_spawn_points == 0:
		return 1.0  # 没有生成点时返回100%，避免阻止补充

	var density = float(spawn_points_with_monsters) / float(total_active_spawn_points)
	return density

# 获取优先级区域列表（性能优化）
func _get_priority_areas() -> Array:
	var priority_areas = []

	# 当前区域最高优先级
	if last_entered_area != "":
		priority_areas.append(last_entered_area)
		area_priority[last_entered_area] = 10

	# 添加所有包含当前区域的生成点区域（修复史莱姆牧场不刷怪的问题）
	for spawn_area in spawn_points.keys():
		if spawn_area in priority_areas:
			continue

		# 检查这个区域是否有包含当前区域的生成点
		var has_current_area_spawn_points = false
		for tile_pos in spawn_points[spawn_area].keys():
			var spawn_point = spawn_points[spawn_area][tile_pos]
			var related_areas = spawn_point.get("related_areas", [])
			if last_entered_area in related_areas and spawn_point.get("is_active", false):
				has_current_area_spawn_points = true
				break

		if has_current_area_spawn_points:
			priority_areas.append(spawn_area)
			# 确保这些区域有合理的优先级
			if not area_priority.has(spawn_area):
				area_priority[spawn_area] = 8

	# 如果优先级区域太多，按优先级排序并限制数量
	if priority_areas.size() > active_area_limit:
		var sorted_areas = priority_areas.duplicate()
		sorted_areas.sort_custom(func(a, b): return area_priority.get(a, 5) > area_priority.get(b, 5))
		priority_areas = sorted_areas.slice(0, active_area_limit)

	return priority_areas

# 检查生成点是否需要补充（原子版：移除is_spawning检查，由外部处理）
func _should_respawn_at_point(area_key: String, tile_pos: Vector2i) -> bool:
	if not spawn_points.has(area_key) or not spawn_points[area_key].has(tile_pos):
		return false

	var spawn_point = spawn_points[area_key][tile_pos]
	var spawn_point_id = spawn_point.get("spawn_point_id", "")

	if spawn_point_id == "":
		return false

	# 1. 检查是否已有有效怪物
	var monster_id = spawn_point_to_monster_id.get(spawn_point_id, -1)
	if monster_id != -1:
		var monster = instance_from_id(monster_id)
		if monster and is_instance_valid(monster):
			# 有有效怪物，不需要补充
			return false
		else:
			# 清理无效的绑定
			spawn_point_to_monster_id.erase(spawn_point_id)
			monster_id_to_spawn_point.erase(monster_id)
			spawn_point.current_monster = null

	# 2. 检查生成点是否在当前玩家区域的预加载范围内
	if not _is_spawn_point_in_area_preload_range(tile_pos, last_entered_area):
		return false

	# 3. 检查是否在异步生成队列中（防止重复添加到队列）
	for queued_task in _async_spawn_queue:
		if queued_task.area_key == area_key and queued_task.tile_pos == tile_pos:
			return false

	# 4. 简化冷却时间检查
	var cooldown = spawn_point.get("spawn_cooldown", 0.0)
	if cooldown > 0.0:
		return false

	return true

# 检查生成点是否对当前玩家区域活跃（或门逻辑：匹配任意预加载区域即可）
func _is_spawn_point_active_for_player(_area_key: String, tile_pos: Vector2i) -> bool:
	if last_entered_area == "":
		return false

	# 获取瓦片数据，直接检查预加载区域
	var tile_data = get_cell_tile_data(tile_pos)
	if not tile_data:
		return false

	# 或门逻辑：检查瓦片的任意预加载区域是否包含当前玩家区域
	for i in 4:
		var preload_area = tile_data.get_custom_data("预加载区域_%d" % (i + 1))
		if preload_area != null and preload_area != "" and preload_area == last_entered_area:
			return true

	return false

# 智能清理：只清理真正不在新区域预加载范围内的怪物
func _smart_cleanup_monsters_for_area_change(new_area: String):
	if new_area == "":
		return

	print("[生成管理器] 智能清理怪物，新区域: %s" % new_area)

	var cleaned_count = 0
	var kept_count = 0
	var total_monsters = 0

	# 遍历所有区域的所有生成点
	for area_key in spawn_points.keys():
		for tile_pos in spawn_points[area_key].keys():
			var spawn_point = spawn_points[area_key][tile_pos]
			var spawn_point_id = spawn_point.get("spawn_point_id", "")

			if spawn_point_id == "":
				continue

			# 检查是否有怪物
			var monster_id = spawn_point_to_monster_id.get(spawn_point_id, -1)
			if monster_id == -1:
				continue

			var monster = instance_from_id(monster_id)
			if not monster or not is_instance_valid(monster):
				# 清理无效绑定
				spawn_point_to_monster_id.erase(spawn_point_id)
				monster_id_to_spawn_point.erase(monster_id)
				spawn_point.current_monster = null
				continue

			total_monsters += 1

			# 检查这个生成点是否仍然在新区域的预加载范围内
			var should_keep_monster = _is_spawn_point_in_area_preload_range(tile_pos, new_area)

			if should_keep_monster:
				# 保留怪物（在新区域的预加载范围内）
				kept_count += 1
				print("[生成管理器] 保留怪物: 区域=%s, 位置=(%d,%d), 仍在新区域预加载范围内" % [area_key, tile_pos.x, tile_pos.y])
			else:
				# 清理怪物
				print("[生成管理器] 清理不在新区域预加载范围内的怪物: 区域=%s, 位置=(%d,%d)" % [area_key, tile_pos.x, tile_pos.y])

				# 安全移除怪物
				if monster.is_inside_tree():
					monster.queue_free()

				# 清理绑定
				spawn_point_to_monster_id.erase(spawn_point_id)
				monster_id_to_spawn_point.erase(monster_id)

				# 清理坐标占用标记
				_clear_position_occupied(tile_pos)

				# 重置生成点状态
				spawn_point.current_monster = null
				spawn_point.is_spawning = false
				spawn_point.spawn_cooldown = SPAWN_COOLDOWN
				spawn_point.spawn_attempts = 0

				cleaned_count += 1

	print("[生成管理器] 智能清理完成: 总怪物数=%d, 保留数=%d, 清理数=%d" % [total_monsters, kept_count, cleaned_count])

# 检查生成点是否在指定区域的预加载范围内
func _is_spawn_point_in_area_preload_range(tile_pos: Vector2i, target_area: String) -> bool:
	var tile_data = get_cell_tile_data(tile_pos)
	if not tile_data:
		return false

	# 检查瓦片的任意预加载区域是否包含目标区域
	for i in 4:
		var preload_area = tile_data.get_custom_data("预加载区域_%d" % (i + 1))
		if preload_area != null and preload_area != "" and preload_area == target_area:
			return true

	return false

# 强制重新扫描所有瓦片，重新构建生成点
func _force_rescan_all_tiles():
	print("[生成管理器] 开始强制重新扫描所有瓦片")

	# 清空现有的生成点数据
	spawn_points.clear()
	_area_spawn_points_cache.clear()

	# 获取所有瓦片
	var used_cells = get_used_cells()
	print("[生成管理器] 找到瓦片数量: %d" % used_cells.size())

	# 遍历所有瓦片，重新构建生成点
	for tile_pos in used_cells:
		var tile_data = get_cell_tile_data(tile_pos)
		if not tile_data:
			continue

		# 获取瓦片的所有预加载区域
		var tile_areas = []
		for i in 4:
			var area_mark = tile_data.get_custom_data("预加载区域_%d" % (i + 1))
			if area_mark != null and area_mark != "":
				tile_areas.append(area_mark)

		if tile_areas.is_empty():
			continue

		# 获取怪物类型
		var monster_types = []
		for i in 3:
			var monster_type = tile_data.get_custom_data("怪物类型_%d" % (i + 1))
			if monster_type != null and monster_type > 0:
				monster_types.append(monster_type)

		if monster_types.is_empty():
			continue

		# 为每个相关区域创建生成点
		for related_area in tile_areas:
			if not spawn_points.has(related_area):
				spawn_points[related_area] = {}

			var spawn_point_id = _generate_spawn_point_id(related_area, tile_pos)
			spawn_points[related_area][tile_pos] = {
				"monster_types": monster_types,
				"current_monster": null,
				"is_spawning": false,
				"max_monsters": MAX_MONSTERS_PER_POINT,
				"spawn_cooldown": 0.0,
				"spawn_attempts": 0,
				"spawn_point_id": spawn_point_id,
				"related_areas": tile_areas.duplicate(),
				"is_active": true
			}

	# 输出统计信息
	for area_key in spawn_points.keys():
		print("[生成管理器] 区域 %s: %d 个生成点" % [area_key, spawn_points[area_key].size()])

# 清理所有不匹配当前玩家区域的怪物（精确清理逻辑）
func _cleanup_monsters_not_matching_player_area():
	if last_entered_area == "":
		return

	print("[生成管理器] 开始清理不匹配玩家区域的怪物，当前玩家区域: %s" % last_entered_area)

	var cleaned_count = 0
	var total_monsters = 0

	# 遍历所有区域的所有生成点
	for area_key in spawn_points.keys():
		for tile_pos in spawn_points[area_key].keys():
			var spawn_point = spawn_points[area_key][tile_pos]
			var spawn_point_id = spawn_point.get("spawn_point_id", "")

			if spawn_point_id == "":
				continue

			# 检查是否有怪物
			var monster_id = spawn_point_to_monster_id.get(spawn_point_id, -1)
			if monster_id == -1:
				continue

			var monster = instance_from_id(monster_id)
			if not monster or not is_instance_valid(monster):
				# 清理无效绑定
				spawn_point_to_monster_id.erase(spawn_point_id)
				monster_id_to_spawn_point.erase(monster_id)
				spawn_point.current_monster = null
				continue

			total_monsters += 1

			# 检查这个生成点是否应该对当前玩家区域活跃
			if not _is_spawn_point_active_for_player(area_key, tile_pos):
				# 不活跃，清理怪物
				print("[生成管理器] 清理非活跃生成点怪物: 区域=%s, 位置=(%d,%d)" % [area_key, tile_pos.x, tile_pos.y])

				# 安全移除怪物
				if monster.is_inside_tree():
					monster.queue_free()

				# 清理绑定
				spawn_point_to_monster_id.erase(spawn_point_id)
				monster_id_to_spawn_point.erase(monster_id)

				# 清理坐标占用标记
				_clear_position_occupied(tile_pos)

				# 重置生成点状态
				spawn_point.current_monster = null
				spawn_point.is_spawning = false
				spawn_point.spawn_cooldown = SPAWN_COOLDOWN
				spawn_point.spawn_attempts = 0

				cleaned_count += 1

	print("[生成管理器] 清理完成: 总怪物数=%d, 清理数=%d, 剩余数=%d" % [total_monsters, cleaned_count, total_monsters - cleaned_count])

# 清理非活跃区域（修复版：正确清理逻辑）
func _cleanup_inactive_areas():
	var areas_to_remove = []

	for area_key in spawn_points.keys():
		if area_key == last_entered_area:
			continue

		# 检查区域优先级（修复：更宽松的清理条件）
		var priority = area_priority.get(area_key, 0)
		if priority <= 0:
			areas_to_remove.append(area_key)
		# 额外检查：如果区域没有活跃的生成点，也清理
		elif not _has_active_spawn_points(area_key):
			areas_to_remove.append(area_key)

	# 清理低优先级区域
	for area_key in areas_to_remove:
		print("[生成管理器] 清理非活跃区域: %s (优先级: %d)" % [area_key, area_priority.get(area_key, 0)])
		_cleanup_area_monsters(area_key)
		spawn_points.erase(area_key)
		area_priority.erase(area_key)

# 检查区域是否有活跃的生成点
func _has_active_spawn_points(area_key: String) -> bool:
	if not spawn_points.has(area_key):
		return false

	for tile_pos in spawn_points[area_key].keys():
		var spawn_point = spawn_points[area_key][tile_pos]
		if spawn_point.get("is_active", false):
			return true

	return false

# 移除旧的_start_batch_respawn_for_area函数，避免重复生成
# 现在统一由_trigger_batch_respawn处理所有生成逻辑

# 移除旧的生成队列处理函数，统一使用多线程异步队列
# 原 _process_optimized_spawn_queue 和 _process_spawn_batch 已被移除
# 现在统一使用 _process_async_spawn_queue 进行多线程优化处理

# 旧的Timer生成队列已移除，统一使用冒险岛式批量补充
# 这个函数已被移除，避免与冒险岛式补充系统冲突

# 初始化区域生成点（修复版：移除重复生成调用）
func initialize_spawn_points(area_key: String):
	# 如果该区域已经初始化过且有生成点，直接返回
	if spawn_points.has(area_key) and not spawn_points[area_key].is_empty():
		return

	# 检查缓存
	if _area_spawn_points_cache.has(area_key):
		spawn_points[area_key] = _area_spawn_points_cache[area_key].duplicate()
		# 移除_start_spawn_queue调用，统一使用冒险岛式补充
		return

	# 清理旧的区域生成点
	spawn_points[area_key] = {}

	# 查找并初始化生成点
	var used_cells = get_used_cells()

	for tile_pos in used_cells:
		var tile_data = get_cell_tile_data(tile_pos)
		if not tile_data:
			continue

		# 检查所有自定义数据字段，看是否有区域匹配
		var is_area_match = false
		var related_areas = []

		# 检查 custom_data_0 到 custom_data_3
		for i in 4:
			var area_mark = tile_data.get_custom_data("预加载区域_%d" % (i + 1))
			if area_mark != null and area_mark != "":
				related_areas.append(area_mark)
				if area_mark == area_key:  # 只匹配当前区域
					is_area_match = true

		# 只有匹配当前区域才创建生成点
		if is_area_match:
			# 获取怪物类型
			var monster_types = []
			for i in 3:
				var monster_type = tile_data.get_custom_data("怪物类型_%d" % (i + 1))
				if monster_type != null and monster_type > 0:
					monster_types.append(monster_type)

			if not monster_types.is_empty():
				var spawn_point_id = _generate_spawn_point_id(area_key, tile_pos)
				spawn_points[area_key][tile_pos] = {
					"monster_types": monster_types,
					"current_monster": null,
					"is_spawning": false,
					"max_monsters": MAX_MONSTERS_PER_POINT,
					"spawn_cooldown": 0.0,
					"spawn_attempts": 0,
					"spawn_point_id": spawn_point_id,
					"related_areas": related_areas,
					"is_area_match": true  # 标记为匹配当前区域
				}

	# 缓存生成点信息
	_area_spawn_points_cache[area_key] = spawn_points[area_key].duplicate()

	# 移除_start_spawn_queue调用，统一使用冒险岛式补充系统
	print("[生成管理器] 区域 %s 初始化完成，生成点数量: %d" % [area_key, spawn_points[area_key].size()])

# 旧的Timer生成系统已移除，现在统一使用冒险岛式批量补充
# 这个函数保留为兼容性接口，但不再创建Timer
func _start_spawn_queue(_area_key: String):
	# 不再创建Timer，统一使用全局冒险岛式补充机制
	pass

# 处理异步生成队列（多线程优化版 + 严格数量控制）
func _process_async_spawn_queue():
	print("[生成管理器] 开始处理异步生成队列，队列长度: %d, 当前生成数: %d" % [_async_spawn_queue.size(), current_spawning_count])

	if _async_spawn_queue.is_empty():
		print("[生成管理器] 异步队列为空，结束处理")
		_is_processing_spawn_queue = false
		return

	# 移除全局生成数量限制检查，直接处理队列

	_is_processing_spawn_queue = true

	# 严格控制批量处理数量，防止掉帧
	var max_batch_size = min(MAX_CONCURRENT_SPAWNS - current_spawning_count, spawn_per_batch)
	var batch_size: int = min(max_batch_size, _async_spawn_queue.size())
	var tasks_to_process: Array = []

	for i in range(batch_size):
		if not _async_spawn_queue.is_empty():
			tasks_to_process.append(_async_spawn_queue.pop_front())

	# 使用多线程处理提高效率
	if _worker_thread_pool_enabled and tasks_to_process.size() > 1:
		print("[生成管理器] 使用多线程处理 %d 个生成任务" % tasks_to_process.size())
		await _process_spawn_tasks_multithreaded(tasks_to_process)
	else:
		# 单线程处理
		print("[生成管理器] 使用单线程处理 %d 个生成任务" % tasks_to_process.size())
		for i in range(tasks_to_process.size()):
			var spawn_task = tasks_to_process[i]
			print("[生成管理器] 调用异步生成: 区域=%s, 位置=(%d,%d), 类型=%d" % [spawn_task.area_key, spawn_task.tile_pos.x, spawn_task.tile_pos.y, spawn_task.monster_type_id])
			current_spawning_count += 1  # 在实际开始处理时才增加计数器
			call_deferred("_async_spawn_monster", spawn_task.area_key, spawn_task.tile_pos, spawn_task.monster_type_id)
			await get_tree().process_frame  # 每个生成任务后都等待一帧
			if i < tasks_to_process.size() - 1:  # 不是最后一个任务
				await get_tree().create_timer(0.05).timeout  # 减少延迟，提高生成效率

	# 继续处理剩余任务，提高生成效率
	if not _async_spawn_queue.is_empty() and current_spawning_count < MAX_CONCURRENT_SPAWNS:
		await get_tree().create_timer(0.3).timeout  # 减少延迟，提高生成效率
		call_deferred("_process_async_spawn_queue")
	else:
		_is_processing_spawn_queue = false

# 多线程处理生成任务
func _process_spawn_tasks_multithreaded(tasks: Array):
	var spawn_results: Array = []

	# 为每个生成任务处理
	for spawn_task in tasks:
		if _current_task_count < _max_concurrent_tasks:
			_thread_mutex.lock()
			_current_task_count += 1
			_thread_mutex.unlock()

			# 直接调用线程函数并收集结果
			var spawn_result: Dictionary = _threaded_monster_spawn(spawn_task)
			spawn_results.append(spawn_result)

			_thread_mutex.lock()
			_current_task_count -= 1
			_thread_mutex.unlock()
		else:
			# 如果线程池满了，回退到主线程处理
			_async_spawn_monster(spawn_task.area_key, spawn_task.tile_pos, spawn_task.monster_type_id)
			await get_tree().process_frame

	# 处理所有生成结果
	for spawn_result in spawn_results:
		_finalize_monster_spawn(spawn_result)

# 线程安全的怪物生成函数
func _threaded_monster_spawn(spawn_task: Dictionary) -> Dictionary:
	var area_key: String = spawn_task.area_key
	var tile_pos: Vector2i = spawn_task.tile_pos
	var monster_type_id: int = spawn_task.monster_type_id

	# 在工作线程中进行数据准备和验证
	var world_pos = map_to_local(tile_pos)
	var global_pos = to_global(world_pos)

	# 获取怪物数据（这个操作是线程安全的）
	var monster_data = GlobalEnemies.get_monster_data(monster_type_id)

	if monster_data.is_empty():
		return {"success": false, "reason": "empty_monster_data"}

	var scene_path: String = monster_data.get("sence", "")
	if scene_path.is_empty():
		return {"success": false, "reason": "empty_scene_path"}

	# 返回准备好的数据，实际的场景实例化需要在主线程进行
	return {
		"success": true,
		"area_key": area_key,
		"tile_pos": tile_pos,
		"global_pos": global_pos,
		"scene_path": scene_path,
		"monster_data": monster_data
	}

# 主线程安全的怪物实例化
func _finalize_monster_spawn(spawn_result: Dictionary):
	if not spawn_result.success:
		# 生成失败时减少计数并清理标记
		current_spawning_count = max(0, current_spawning_count - 1)
		var area_key = spawn_result.get("area_key", "")
		var tile_pos = spawn_result.get("tile_pos", Vector2i.ZERO)

		# 移除全局锁清理

		if spawn_points.has(area_key) and spawn_points[area_key].has(tile_pos):
			spawn_points[area_key][tile_pos]["is_spawning"] = false
			# 增加冷却时间，避免立即重试
			spawn_points[area_key][tile_pos]["spawn_cooldown"] = SPAWN_COOLDOWN
		return

	# 在主线程中实例化怪物（使用延迟调用避免物理查询冲突）
	var monster = await _deferred_spawn_monster_safe(spawn_result)

	if monster and spawn_points.has(spawn_result.area_key) and spawn_points[spawn_result.area_key].has(spawn_result.tile_pos):
		var spawn_point = spawn_points[spawn_result.area_key][spawn_result.tile_pos]
		var spawn_point_id_from_point = spawn_point.get("spawn_point_id", "")

		if spawn_point_id_from_point != "":
			register_monster_to_spawn_point(monster, spawn_point_id_from_point)

		spawn_point.current_monster = weakref(monster)
		spawn_point.is_spawning = false
		spawn_point.spawn_attempts = 0

		# 移除全局锁清理

		# 生成成功时减少计数（只有成功时才减少）
		current_spawning_count = max(0, current_spawning_count - 1)

		# 记录生成时间（使用更简单的方法）
		last_spawn_time = Time.get_unix_time_from_system()

		# 连接怪物死亡信号
		if monster.has_signal("died"):
			monster.connect("died", func():
				_on_monster_died(monster, spawn_point_id_from_point)
			)
	else:
		print("[生成管理器] 怪物实例化失败: 区域=%s, 位置=%s" % [spawn_result.area_key, spawn_result.tile_pos])

# 异步生成怪物（优化版，支持多线程预处理）
func _async_spawn_monster(area_key: String, tile_pos: Vector2i, monster_type: int):
	print("[生成管理器] 异步生成怪物开始: 区域=%s, 位置=(%d,%d), 类型=%d" % [area_key, tile_pos.x, tile_pos.y, monster_type])

	# 如果启用了多线程，先在工作线程中进行数据准备
	if _worker_thread_pool_enabled and _current_task_count < _max_concurrent_tasks:
		_thread_mutex.lock()
		_current_task_count += 1
		_thread_mutex.unlock()

		var spawn_task = {
			"area_key": area_key,
			"tile_pos": tile_pos,
			"monster_type_id": monster_type
		}

		# 直接调用线程函数并获取结果
		var spawn_result: Dictionary = _threaded_monster_spawn(spawn_task)

		_thread_mutex.lock()
		_current_task_count -= 1
		_thread_mutex.unlock()

		# 在主线程中完成实例化
		_finalize_monster_spawn(spawn_result)
		return

	# 回退到原始的单线程方法
	var world_pos = map_to_local(tile_pos)
	var global_pos = to_global(world_pos)

	# 使用延迟加载获取怪物数据
	var monster_data = GlobalEnemies.get_monster_data(monster_type)

	if monster_data.is_empty():
		print("[生成管理器] 怪物数据为空: 区域=%s, 位置=(%d,%d), 类型=%d" % [area_key, tile_pos.x, tile_pos.y, monster_type])
		if spawn_points.has(area_key) and spawn_points[area_key].has(tile_pos):
			spawn_points[area_key][tile_pos]["is_spawning"] = false
			spawn_points[area_key][tile_pos]["spawn_cooldown"] = SPAWN_COOLDOWN
		current_spawning_count = max(0, current_spawning_count - 1)
		return

	var scene_path: String = monster_data.get("sence", "")
	if scene_path.is_empty():
		print("[生成管理器] 场景路径为空: 区域=%s, 位置=(%d,%d), 类型=%d" % [area_key, tile_pos.x, tile_pos.y, monster_type])
		if spawn_points.has(area_key) and spawn_points[area_key].has(tile_pos):
			spawn_points[area_key][tile_pos]["is_spawning"] = false
			spawn_points[area_key][tile_pos]["spawn_cooldown"] = SPAWN_COOLDOWN
		current_spawning_count = max(0, current_spawning_count - 1)
		return

	# 使用主线程安全的方法生成怪物
	var monster = await _deferred_spawn_monster(area_key, tile_pos, global_pos, scene_path, monster_data)

	# 如果生成成功，更新生成点状态
	if monster and spawn_points.has(area_key) and spawn_points[area_key].has(tile_pos):
		print("[生成管理器] 怪物生成并注册成功: 区域=%s, 位置=(%d,%d)" % [area_key, tile_pos.x, tile_pos.y])
		var spawn_point = spawn_points[area_key][tile_pos]
		var spawn_point_id_from_point = spawn_point.get("spawn_point_id", "")

		# 使用ID绑定注册怪物
		if spawn_point_id_from_point != "":
			register_monster_to_spawn_point(monster, spawn_point_id_from_point)

		spawn_point.current_monster = weakref(monster)  # 保留弱引用作为备用
		spawn_point.is_spawning = false  # 清除生成标志
		spawn_point.spawn_attempts = 0  # 重置生成尝试次数

		# 生成成功时减少计数
		current_spawning_count = max(0, current_spawning_count - 1)

		# 连接怪物死亡信号
		if monster.has_signal("died"):
			monster.connect("died", func():
				_on_monster_died(monster, spawn_point_id_from_point)
			)
	else:
		# 生成失败，清理标记并减少计数
		print("[生成管理器] 怪物生成失败，清理状态: 区域=%s, 位置=(%d,%d)" % [area_key, tile_pos.x, tile_pos.y])
		if spawn_points.has(area_key) and spawn_points[area_key].has(tile_pos):
			spawn_points[area_key][tile_pos].current_monster = null
			spawn_points[area_key][tile_pos].is_spawning = false
			spawn_points[area_key][tile_pos].spawn_cooldown = SPAWN_COOLDOWN
		current_spawning_count = max(0, current_spawning_count - 1)

# 安全的延迟生成怪物（避免物理查询冲突）
func _deferred_spawn_monster_safe(spawn_result: Dictionary):
	# 等待一帧，确保物理查询完成
	await get_tree().process_frame

	var monster = GlobalEnemies.spawn_monster_with_animation(
		spawn_result.global_pos,
		spawn_result.scene_path,
		spawn_result.monster_data,
		get_tree().current_scene
	)

	return monster

# 延迟生成怪物（添加调试信息）
func _deferred_spawn_monster(area_key: String, tile_pos: Vector2i, global_pos: Vector2, scene_path: String, monster_data: Dictionary):
	print("[生成管理器] 开始生成怪物: 区域=%s, 位置=(%d,%d), 场景=%s" % [area_key, tile_pos.x, tile_pos.y, scene_path])

	var monster = GlobalEnemies.spawn_monster_with_animation(
		global_pos,
		scene_path,
		monster_data,
		get_tree().current_scene
	)

	if monster:
		print("[生成管理器] 怪物生成成功: 区域=%s, 位置=(%d,%d)" % [area_key, tile_pos.x, tile_pos.y])
	else:
		print("[生成管理器] 怪物生成失败: 区域=%s, 位置=(%d,%d), 场景=%s" % [area_key, tile_pos.x, tile_pos.y, scene_path])

	return monster

# 更新活跃区域列表
func _update_active_areas(current_area: String):
	# 清空当前预加载区域
	preload_areas.clear()
	
	# 添加当前区域
	preload_areas.append(current_area)
	
	# 查找所有与当前区域共享生成点的区域
	for spawn_point_id in spawn_point_areas.keys():
		var areas = spawn_point_areas[spawn_point_id]
		
		# 使用模糊匹配（支持区域前缀）
		for area in areas:
			if current_area.begins_with(area) or area.begins_with(current_area):
				if area not in preload_areas:
					preload_areas.append(area)

# 检查区域是否应该保持预加载
func _should_keep_area_preloaded(area_key: String) -> bool:
	# 如果区域在预加载列表中，保持预加载
	if area_key in preload_areas:
		return true
	
	# 检查该区域是否是新区域（没有生成点缓存）
	if not _area_spawn_points_cache.has(area_key):
		# 新区域没有生成点，不应该保持预加载
		return false
	
	# 检查该区域的生成点是否与任何预加载区域共享
	if area_spawn_points.has(area_key):
		var spawn_points_in_area = area_spawn_points[area_key]
		for spawn_point_id in spawn_points_in_area:
			if spawn_point_areas.has(spawn_point_id):
				var point_areas = spawn_point_areas[spawn_point_id]
				# 检查这个生成点是否属于任何预加载区域
				for point_area in point_areas:
					if point_area in preload_areas:
						return true
	
	return false

# 获取地图的所有预加载区域
func get_preloaded_areas_for_map(map_key: String) -> Array:
	var areas = []
	
	# 默认至少包含当前区域
	if last_entered_area != "" and last_entered_area != null:
		areas.append(last_entered_area)
	
	# 根据地图键值返回预定义的区域列表
	match map_key:
		"map_wilderness":
			areas.append_array(["area_forest", "area_river", "area_mountains"])
		"map_city":
			areas.append_array(["area_market", "area_palace", "area_slums"])
		_:
			# 对于未知地图，只返回当前区域
			pass
	
	return areas

# 移除_ensure_spawn_point_has_monster函数，避免与冒险岛式补充系统冲突
# 统一由_trigger_batch_respawn处理所有生成逻辑

# 清理单个生成点的怪物
func _cleanup_spawn_point(area_key: String, tile_pos: Vector2i):
	var spawn_point = spawn_points[area_key][tile_pos]
	var spawn_point_id = spawn_point.get("spawn_point_id", "")
	
	if spawn_point_id != "":
		var monster_id = spawn_point_to_monster_id.get(spawn_point_id, -1)
		if monster_id != -1:
			var monster = instance_from_id(monster_id)
			if monster and is_instance_valid(monster):
				# 安全移除怪物
				if monster.is_inside_tree():
					monster.queue_free()
				
				# 清理绑定
				spawn_point_to_monster_id.erase(spawn_point_id)
				monster_id_to_spawn_point.erase(monster_id)
				
				# 清理坐标占用标记
				_clear_position_occupied(tile_pos)
	
	# 重置生成点状态
	spawn_point["current_monster"] = null
	spawn_point["is_spawning"] = false
	spawn_point["spawn_cooldown"] = SPAWN_COOLDOWN
	spawn_point["spawn_attempts"] = 0
	spawn_point["is_active"] = false

# 移除重复的补充函数，统一使用冒险岛式批量补充
# 这些函数与_trigger_batch_respawn冲突，导致重复生成
# 现在统一由_trigger_batch_respawn在全局定时器中处理

# 所有调试和性能监控函数已删除，提高游戏性能

# 动态调整性能参数
func adjust_performance_settings():
	var current_fps = Engine.get_frames_per_second()

	# 根据帧率动态调整参数（移除queue_interval，统一使用冒险岛式补充）
	if current_fps < 20:
		# 严重卡顿，大幅降低性能要求
		spawn_per_batch = 1
		global_replenish_interval = 8.0
	elif current_fps < 30:
		# 轻微卡顿，适度降低性能要求
		spawn_per_batch = max(1, spawn_per_batch - 1)
		global_replenish_interval = 6.0
	elif current_fps > 50:
		# 性能良好，可以提高处理效率
		spawn_per_batch = min(_max_concurrent_tasks, spawn_per_batch + 1)
		global_replenish_interval = 3.0  # 进一步提高补充频率

# 启用/禁用多线程处理
func set_multithreading_enabled(enable_multithreading: bool):
	_worker_thread_pool_enabled = enable_multithreading

# 启用/禁用GPU计算
func set_gpu_compute_enabled(enable_gpu: bool):
	if enable_gpu and not _compute_shader_enabled:
		_initialize_gpu_compute()
	elif not enable_gpu:
		_compute_shader_enabled = false
